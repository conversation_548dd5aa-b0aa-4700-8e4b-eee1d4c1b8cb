main:
  push:
    - docker:
        image: node:18
      imports: https://cnb.cool/godgodgame/oci-private-key/-/blob/main/envs.yml
      stages:
        - name: 将mian分支同步更新到igiven的main分支
          script: git push https://$CNB_TOKEN_GK:$<EMAIL>/igiven/microsoft-reward.git HEAD:main
        - name: 将mian分支同步更新到igiven的main分支
          script: git push https://$CNB_TOKEN_GK:$<EMAIL>/zhepama/microsoft-reward.git HEAD:main
        - name: 构建并打包
          script: | 
            mkdir archive
            tar --exclude='./archive' --exclude='.git' -zcvf archive/release.tar.gz .
        - name: ansible发布
          image: plugins/ansible
          settings:
            private_key: $SERVER_PRIVATE_KEY
            inventory: hosts
            playbook: playbook.yml


$:
  vscode:
    - docker:
        #image: node:20
        build:
          dockerfile: .ide/Dockerfile
          by:
            - .ide/settings.json
            - .ide/cnb-init-from
            - .ide/cnb-init-from-without-lfs
            - .ide/gitconfig
      env:
        CNB_WELCOME_EXECUTE_COMMAND: echo "Welcome to My CNB!"
      services:
        - vscode
        - docker
      # 开发环境启动后会执行的任务
      stages:
        - name: 软链文件到工作目录（仓库根目录）
          # ./ 是工作目录，默认为 /workspace
          script: |
            if [ -e "/root/.cnb/ms-sessions" ]; then
                ln -sf /root/.cnb/ms-sessions $HOME/sessions
            else
                echo "ms-sessions不存在"
            fi

            if [ -e "/root/.cnb/.env.microsoft-reward-godgodgame" ]; then
                ln -sf /root/.cnb/.env.microsoft-reward-godgodgame /workspace/.env.local
            else
                echo ".env.microsoft-reward-godgodgame不存在"
            fi


        - name: 安装依赖和构建
          script: |
            cd /workspace
            npm config set registry https://registry.npmmirror.com
            npm install 
            npx playwright install chromium
            npm run build
            mkdir -p $HOME/workspace
            mkdir -p $HOME/deploy
            mkdir -p $HOME/logs
            mkdir -p $HOME/sessions
            mkdir -p $HOME/cache
            ln -s  /workspace  /root/workspace/microsoft-rewards-script
            chmod +x $HOME/workspace/microsoft-rewards-script/scripts/*.sh
            ./scripts/setup-crontab.sh
            service cron start

      # 开发环境销毁前会执行该任务,注释掉..每次关闭非常耗时间
      # endStages:
      #   - name: end stage 1
      #     script: echo "end stage 1"