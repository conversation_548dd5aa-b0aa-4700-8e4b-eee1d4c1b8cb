#https://cnb.cool/cnb/cool/default-dev-env/-/blob/main/Dockerfile
#https://docs.cnb.cool/zh/workspaces/custom-dev-env.html

FROM ubuntu:22.04

# 设置非交互式安装，避免 tzdata 等提问
ENV DEBIAN_FRONTEND=noninteractive

# 设置时区为上海时间
ENV TZ=Asia/Shanghai


# 指定字符集支持命令行输入中文（根据需要选择字符集）
ENV LANG C.UTF-8
ENV LANGUAGE C.UTF-8
ENV LC_ALL C.UTF-8

# 替换为腾讯云的 APT 镜像源
# 首先安装 ca-certificates 以支持 https 源，lsb-release 用于获取发行版代号
RUN apt-get update && apt-get install -y ca-certificates lsb-release && \
    echo "deb https://mirrors.cloud.tencent.com/ubuntu/ $(lsb_release -cs) main restricted universe multiverse" > /etc/apt/sources.list && \
    echo "deb https://mirrors.cloud.tencent.com/ubuntu/ $(lsb_release -cs)-updates main restricted universe multiverse" >> /etc/apt/sources.list && \
    echo "deb https://mirrors.cloud.tencent.com/ubuntu/ $(lsb_release -cs)-backports main restricted universe multiverse" >> /etc/apt/sources.list && \
    echo "deb https://mirrors.cloud.tencent.com/ubuntu/ $(lsb_release -cs)-security main restricted universe multiverse" >> /etc/apt/sources.list

# 安装 code-server 和扩展
# 注意修改版本时，需要同步修改 languagepacks.json 文件内容(重新生成) - 如果此文件重要，请 COPY 进来
RUN apt-get update && apt-get install -y curl && \
    curl -fsSL https://code-server.dev/install.sh | sh -s -- --version 4.99.2
    # code-server --install-extension cnbcool.cnb-welcome && \
    # code-server --install-extension redhat.vscode-yaml && \
    # code-server --install-extension waderyan.gitblame && \
    # code-server --install-extension mhutchie.git-graph && \
    # code-server --install-extension donjayamanne.githistory && \
    # code-server --install-extension cloudstudio.live-server && \
    # code-server --install-extension tencent-cloud.coding-copilot


# 安装 Node.js 最新 LTS 版本和其他工具
RUN apt-get update && \
    # 安装 Node.js (使用 NodeSource 官方推荐方法安装最新 LTS 版本)
    # 如果需要安装最新的 Current 版本，可以将 setup_lts.x 改为 setup_current.x
    # 如果需要特定主版本，例如 Node 20.x: curl -fsSL https://deb.nodesource.com/setup_20.x | bash -
    echo "正在设置 NodeSource 仓库并安装 Node.js 最新 LTS 版本..." && \
    curl -fsSL https://deb.nodesource.com/setup_lts.x | bash - && \
    apt-get install -y --no-install-recommends git rsync jq git-lfs vim curl wget unzip lsof nload htop net-tools dnsutils openssh-server cron sudo nodejs tzdata && \
    echo "Node.js 版本:" && node -v && \
    echo "npm 版本:" && npm -v && \
    npm config set registry https://registry.npmmirror.com && \
    # 使用特定版本安装,不指定会使用最新Playwright,避免在项目目录安装了不同版本(会重新安装,会有多个版本)
    npx playwright@1.51.0 install-deps && \
    npx playwright@1.51.0 install chromium
 
COPY .ide/settings.json /root/.local/share/code-server/Machine/settings.json
COPY .ide/cnb-init-from /bin/cnb-init-from
COPY .ide/cnb-init-from-without-lfs /bin/cnb-init-from-without-lfs
COPY .ide/gitconfig /root/.gitconfig

