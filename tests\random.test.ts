import { describe, expect, it, test } from 'vitest';
import { shuffleArray, getRandomInRange, weightShuffleArray } from '../src/utils/random';

describe('weightShuffleArray', () => {
    it('should return an empty array when input is empty', () => {
        expect(weightShuffleArray([])).toEqual([]);
    });

    it('should use normal shuffle when all weights are 0 or undefined', () => {
        const array = [
            { id: 1, weight: 0 },
            { id: 2, weight: 0 },
            { id: 3 }
        ];
        const result = weightShuffleArray(array);
        expect(result.length).toBe(array.length);
        expect(result).toContainEqual(array[0]);
        expect(result).toContainEqual(array[1]);
        expect(result).toContainEqual(array[2]);
    });

    it('should respect weights in randomization', () => {
        // Create an array with heavily weighted first item
        const array = [
            { id: 1, weight: 1000 },  // Should appear first most of the time
            { id: 2, weight: 1 },
            { id: 3, weight: 1 }
        ];

        // Run multiple trials to verify statistical behavior
        const trials = 1000;
        let firstPositionCount = 0;

        for (let i = 0; i < trials; i++) {
            const result = weightShuffleArray(array);
            if (result[0].id === 1) {
                firstPositionCount++;
            }
        }

        // With weight ratio of 1000:1:1, item 1 should appear first ~99% of the time
        // We'll use a more lenient threshold of 90% to account for randomness
        const percentage = firstPositionCount / trials;
        expect(percentage).toBeGreaterThan(0.9);
    });

    it('should maintain item uniqueness', () => {
        const array = [
            { id: 1, weight: 5 },
            { id: 2, weight: 3 },
            { id: 3, weight: 2 }
        ];
        
        const result = weightShuffleArray(array);
        
        // Check no duplicates
        const ids = result.map(item => item.id);
        const uniqueIds = [...new Set(ids)];
        expect(ids.length).toBe(uniqueIds.length);
        
        // Check all items present
        expect(result.length).toBe(array.length);
        array.forEach(item => {
            expect(result).toContainEqual(item);
        });
    });
});

describe('shuffleArray', () => {
    it('should maintain array length', () => {
        const array = [1, 2, 3, 4, 5];
        expect(shuffleArray(array).length).toBe(array.length);
    });

    it('should contain all original elements', () => {
        const array = [1, 2, 3, 4, 5];
        const shuffled = shuffleArray(array);
        array.forEach(element => {
            expect(shuffled).toContain(element);
        });
    });

    it('should generate different orders with different seeds', () => {
        const array = [1, 2, 3, 4, 5];
        const result1 = shuffleArray(array, 123);
        const result2 = shuffleArray(array, 456);
        expect(result1).not.toEqual(result2);
    });

    it('should generate same order with same seed', () => {
        const array = [1, 2, 3, 4, 5];
        const result1 = shuffleArray(array, 123);
        const result2 = shuffleArray(array, 123);
        expect(result1).toEqual(result2);
    });

    it('should throw error for invalid seeds', () => {
        const array = [1, 2, 3];
        expect(() => shuffleArray(array, 3.14)).toThrow('Seed must be an integer');
        expect(() => shuffleArray(array, 0)).toThrow('Seed cannot be 0');
    });
});

describe('getRandomInRange', () => {
    it('should return number within specified range', () => {
        const min = 1;
        const max = 10;
        const trials = 1000;
        
        for (let i = 0; i < trials; i++) {
            const result = getRandomInRange(min, max);
            expect(result).toBeGreaterThanOrEqual(min);
            expect(result).toBeLessThanOrEqual(max);
            expect(Number.isInteger(result)).toBe(true);
        }
    });

    it('should work with negative numbers', () => {
        const min = -10;
        const max = -1;
        const trials = 1000;
        
        for (let i = 0; i < trials; i++) {
            const result = getRandomInRange(min, max);
            expect(result).toBeGreaterThanOrEqual(min);
            expect(result).toBeLessThanOrEqual(max);
            expect(Number.isInteger(result)).toBe(true);
        }
    });

    it('should handle min equal to max', () => {
        const number = 5;
        expect(getRandomInRange(number, number)).toBe(number);
    });
});
