import * as dotenv from 'dotenv';
import { AppConfig, ProofConfig } from '../types/index.js';
import { resolvePath } from '../utils/path.js';
import * as fs from 'fs';
import * as path from 'path';
import { parse as parseJsonc } from 'jsonc-parser';
import { fileURLToPath } from 'url';
import { ProxyAgent, Agent, setGlobalDispatcher, fetch } from 'undici';
import os from 'os';

export class Config {
    private static instance: Config;
    private config: AppConfig;
    private directAgent = new Agent();
    private constructor() {
        // 读取 JSONC 配置文件
        const configPath = path.join(path.dirname(path.dirname(fileURLToPath(import.meta.url))), 'config.jsonc');
        const configContent = fs.readFileSync(configPath, 'utf-8');
        const baseConfig: AppConfig = parseJsonc(configContent);

        this.loadEnvironmentFiles();
        // 使用导入的默认配置
        // let baseConfig: AppConfig = defaultConfig;
        // 处理 proof tokens
        if (baseConfig.proof) {
            baseConfig.proof = baseConfig.proof.map(proof => {
                const tokenKey = proof.suffix.replace(/\./g, '_').toUpperCase() + '_TOKEN';
                return {
                    ...proof,
                    token: process.env[tokenKey] || proof.token
                };
            });
        }

        // 环境变量覆盖其他配置
        this.config = {
            startHour: process.env.START_HOUR ? process.env.START_HOUR : baseConfig.startHour,
            serverId: process.env.SERVER_ID || baseConfig.serverId,
            browser: {
                globalTimeout: parseInt(process.env.GLOBAL_TIMEOUT || baseConfig.browser.globalTimeout.toString()),
                headless: process.env.HEADLESS === 'true' || baseConfig.browser.headless,
                region: process.env.REGION || baseConfig.browser.region,
                sessionDir: resolvePath(process.env.SESSION_DIR || baseConfig.browser.sessionDir),
                fingerprintExpirationDays: parseInt(process.env.FINGERPRINT_EXPIRATION_DAYS || baseConfig.browser.fingerprintExpirationDays.toString()),
                proxy: {
                    server: process.env.PROXY_SERVER || baseConfig.browser.proxy.server,
                    username: process.env.PROXY_USERNAME || baseConfig.browser.proxy.username,
                    password: process.env.PROXY_PASSWORD || baseConfig.browser.proxy.password
                },
                cache: {
                    path: resolvePath(process.env.CACHE_DIR || baseConfig.browser.cache.path),
                    enable: process.env.CACHE_ENABLE === 'true' || baseConfig.browser.cache.enable,
                    timeToLive: parseInt(process.env.CACHE_TTL || baseConfig.browser.cache.timeToLive.toString())
                },
            },
            log: {
                localDir: resolvePath(process.env.LOG_DIR || baseConfig.log.localDir),
                saveDays: parseInt(process.env.LOG_SAVE_DAYS || baseConfig.log.saveDays.toString())
            },
            proof: baseConfig.proof,
            account: {
                apiToken: process.env.ACCOUNT_API_TOKEN || baseConfig.account.apiToken,
                apiUrl: process.env.ACCOUNT_API_URL || baseConfig.account.apiUrl,
                distributionMode: (process.env.ACCOUNT_DISTRIBUTION_MODE as "all" | "random" | "even" | undefined) || baseConfig.account.distributionMode,
                distributionCycleDays: parseInt(process.env.ACCOUNT_DISTRIBUTION_CYCLE_DAYS || baseConfig.account.distributionCycleDays.toString()),
                runningGroup: parseInt(process.env.RUNNING_GROUP || baseConfig.account.runningGroup.toString()),
                runningCount: parseInt(process.env.RUNNING_COUNT || baseConfig.account.runningCount.toString()),
                runningSlotMinutes: parseInt(process.env.RUNNING_SLOT_MINUTES || baseConfig.account.runningSlotMinutes.toString()),
                runningMode: (process.env.ACCOUNT_RUNNING_MODE as "all" | "group" | "count" | undefined) || baseConfig.account.runningMode,
                dailyTasksSeparately: process.env.DAILY_TASKS_SEPARATELY === 'true' || baseConfig.account.dailyTasksSeparately,
                dailyTasksHour: parseInt(process.env.DAILY_TASKS_HOUR || baseConfig.account.dailyTasksHour.toString()),
                execLimits: baseConfig.account.execLimits
            },
            reward: {
                country: process.env.REWARD_COUNTRY || baseConfig.reward.country,
                language: process.env.REWARD_LANGUAGE || baseConfig.reward.language,
                doWaitMin: parseInt(process.env.DO_WAIT_MIN || baseConfig.reward.doWaitMin.toString()),
                doWaitMax: parseInt(process.env.DO_WAIT_MAX || baseConfig.reward.doWaitMax.toString())
            },
            search: {
                ...baseConfig.search,
            }
        };

        const proxyUrl = this.buildProxyUrl();
        if (proxyUrl) {
            const proxyAgent = new ProxyAgent(proxyUrl);
            setGlobalDispatcher(proxyAgent);
        }

    }

    public async loadRemoteConfig() {
        //加载远程日志
        const url = `${this.config.account.apiUrl}/server/config?id=${this.config.serverId}`;
        let remoteConfig: any = {};
        let attempt = 0;
        while (attempt < 10) {
            attempt++;
            try {
                const response = await fetch(url, {
                    method: 'GET',
                    headers: {
                        'Authorization': `Bearer ${this.config.account.apiToken}`,
                        'Content-Type': 'application/json'
                    },
                    dispatcher: this.directAgent
                });
                if (response.ok) {
                    remoteConfig = await response.json() as any;
                    break;
                }
            } catch {
                // 忽略错误，继续重试
            }
            await new Promise(resolve => setTimeout(resolve, 1000));
        }
        this.config = {
            ...this.config,
            ...remoteConfig,
            browser: { ...this.config.browser, ...(remoteConfig.browser || {}) },
            log: { ...this.config.log, ...(remoteConfig.log || {}) },
            account: { ...this.config.account, ...(remoteConfig.account || {}) },
            proof:(() => {
                // 创建一个新的数组来存储最终结果
                const mergedProof = [...this.config.proof];
                
                // 如果remoteConfig.proof存在
                if (remoteConfig.proof && Array.isArray(remoteConfig.proof)) {
                  // 遍历远程配置中的每一项
                  remoteConfig.proof.forEach((remoteItem:ProofConfig) => {
                    // 查找本地配置中是否已存在相同suffix的项
                    const existingIndex = mergedProof.findIndex(item => item.suffix === remoteItem.suffix);
                    
                    if (existingIndex !== -1) {
                        // 如果存在，则合并属性（补充而不是覆盖）
                        mergedProof[existingIndex] = {
                          ...mergedProof[existingIndex],  // 保留原有属性
                          ...remoteItem                   // 添加或更新新属性
                        };
                      } else {
                        // 如果不存在，则追加到数组末尾
                        mergedProof.push(remoteItem);
                      }
                  });
                }
                return mergedProof;
              })(),
            reward: { ...this.config.reward, ...(remoteConfig.reward || {}) },
            search: { ...this.config.search, ...(remoteConfig.search || {}) },
        };
        console.log('Config loaded:', JSON.stringify(this.config, null, 2));
    }

    buildProxyUrl() {
        const { server, username, password } = this.config.browser.proxy || {};

        if (!server) return null;

        try {
            const url = new URL(server);

            // 处理认证信息
            if (username) {
                url.username = encodeURIComponent(username);
                // 密码可能为空的情况
                url.password = password ? encodeURIComponent(password) : '';
            }

            return url.toString();
        } catch (error) {
            console.error('Invalid proxy server URL:', error);
            return null;
        }
    };



    loadEnvironmentFiles(): boolean {
        const homeDir = os.homedir() || process.env.HOME || process.env.USERPROFILE || "";
        const currentDir = process.cwd();
        const entryDir = path.dirname(path.dirname(fileURLToPath(import.meta.url)));

        //dotenv.config(); // 加载项目根目录的 .env 文件

        
        // 定义搜索路径顺序
        const envPaths = [
            path.join(entryDir, '.env'),
            path.join(entryDir, '.env.local'),
            path.join(currentDir, '.env'), 
            path.join(currentDir, '.env.local'),
            path.join(homeDir, 'deploy', '.env'),
            path.join(homeDir, 'deploy', '.env.local'),
            path.join(homeDir, '.env'),
            path.join(homeDir, '.env.local'),
        ];

        let loadedAnyFile = false;
        // 遍历所有路径并加载存在的 .env 文件
        for (const envPath of envPaths) {
            if (fs.existsSync(envPath)) {
                try {
                    dotenv.config({
                        path: envPath,
                        override: true  //允许后面的覆盖前面的
                    });
                    console.log(`成功加载环境文件: ${envPath}`);
                    loadedAnyFile = true;
                } catch (error) {
                    console.error(`加载环境文件 ${envPath} 时出错:`, error);
                }
            }
            else {
                console.log(`环境文件不存在: ${envPath}`);
            }
        }

        if (!loadedAnyFile) {
            console.warn('未找到任何 .env 文件');
        }
        return loadedAnyFile;
    }

    public static getInstance(): Config {
        if (!Config.instance) {
            Config.instance = new Config();
        }
        return Config.instance;
    }

    public getConfig(): AppConfig {
        return this.config;
    }

    public getLimitByScore(score: number) {
        const searchLimits = this.config.search.searchLimits;
        if (searchLimits) {
            for (const limit of searchLimits) {
                if (score >= limit.minScore && score < limit.maxScore) {
                    return limit;
                }
            }
        }
        return null;
    }
}