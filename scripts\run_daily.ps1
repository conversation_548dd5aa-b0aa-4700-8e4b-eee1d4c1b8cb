# run_daily.ps1
# ./scripts/run_daily.ps1
# 计算机管理->任务计划程序 -> 添加计划
# 为了不显示窗口,选中两者中的一个 1. "不管用户是否登录都要运行","不存储密码"  2."以最高权限运行"
# 程序/脚本："C:\Program Files\PowerShell\7-preview\pwsh.exe"  # 通过GET-Command pwsh查看
# -NoProfile: 不加载 PowerShell 配置文件,加快启动速度  
# -NonInteractive: 禁用交互式提示 
# -NoLogo: 不显示 PowerShell 启动标志 
# -WindowStyle Hidden: 完全隐藏窗口
# -ExecutionPolicy Bypass: 绕过执行策略限制
# 添加参数：-NoProfile -NonInteractive -NoLogo -WindowStyle Hidden -ExecutionPolicy Bypass -File "D:\Workspace\microsoft-reward\scripts\run_daily.ps1"
# 起始于: D:\Workspace\microsoft-reward

# 关闭所有名为 'Google Chrome for Testing' 的进程
#Get-Process | Where-Object {$_.Name -like "*chrome*" -or $_.Name -like "*chromium*"} | Stop-Process -Force

# 切换到指定目录并执行命令，将输出重定向到日志文件
Set-Location -Path "D:\Workspace\microsoft-reward"

#node scripts/backup.js ./src
node scripts/log.js ./dist/logs
npm run preview  



 