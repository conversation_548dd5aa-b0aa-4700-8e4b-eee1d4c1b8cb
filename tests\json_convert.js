import * as fs from 'fs';
import * as path from 'path';
import { fileURLToPath } from 'url';
import { dirname } from 'path';

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);

const convert = () => {
    // 读取old.json
    const oldData = JSON.parse(
        fs.readFileSync(path.join(__dirname, 'old.json'), 'utf-8')
    );
    console.log(oldData.length);
    // 找到*************************的索引
    const targetIndex = oldData.findIndex(item => item.email === '<EMAIL>');

    // 转换数据
    const newData = oldData.map((item, index) => {
        const newAccount = {
            email: item.email,
            password: item.password,
            proofEmail: ''
        };

        // 在目标索引之后的元素添加proofEmail
        if (index > targetIndex) {
            const emailPrefix = item.email.split('@')[0];
            newAccount.proofEmail = `${emailPrefix}@godgodgame.com`;
        }

        return newAccount;
    });

    // 保存到new.json
    fs.writeFileSync(
        path.join(__dirname, 'new.json'),
        JSON.stringify(newData, null, 4)
    );
};

convert();
