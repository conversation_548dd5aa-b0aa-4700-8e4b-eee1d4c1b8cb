{"$schema": "http://json-schema.org/draft-07/schema#", "definitions": {"ProofConfig": {"properties": {"apiUrl": {"type": "string"}, "suffix": {"type": "string"}, "token": {"type": "string"}}, "type": "object"}}, "properties": {"startHour": {"type": "string", "description": "程序开始执行的小时,可以填入时间,也可以是范围"}, "account": {"properties": {"apiToken": {"type": "string"}, "apiUrl": {"type": "string"}, "dailyTasksHour": {"type": "number"}, "dailyTasksSeparately": {"type": "boolean"}, "distributionCycleDays": {"type": "number"}, "distributionMode": {"enum": ["all", "even", "random"], "type": "string"}, "execLimits": {"items": {"properties": {"count": {"type": "number"}, "maxScore": {"type": "number"}, "minScore": {"type": "number"}}, "type": "object"}, "type": "array"}, "runningSlotMinutes": {"type": "number"}, "runningCount": {"type": "number"}, "runningGroup": {"type": "number"}, "runningMode": {"enum": ["all", "group", "count"], "type": "string"}}, "type": "object"}, "browser": {"properties": {"cache": {"properties": {"enable": {"type": "boolean"}, "path": {"type": "string"}, "timeToLive": {"type": "number"}}, "type": "object"}, "fingerprintExpirationDays": {"type": "number"}, "globalTimeout": {"type": "number"}, "headless": {"type": "boolean"}, "proxy": {"properties": {"password": {"type": "string"}, "server": {"type": "string"}, "username": {"type": "string"}}, "type": "object"}, "sessionDir": {"type": "string"}}, "type": "object"}, "log": {"properties": {"localDir": {"type": "string"}, "saveDays": {"type": "number"}}, "type": "object"}, "proof": {"items": {"$ref": "#/definitions/ProofConfig"}, "type": "array"}, "reward": {"properties": {"country": {"type": "string"}, "doWaitMax": {"type": "number"}, "doWaitMin": {"type": "number"}, "language": {"type": "string"}}, "type": "object"}, "search": {"properties": {"apiUrl": {"type": "string"}, "clickRandomResults": {"type": "boolean"}, "items": {"items": {"type": "string"}, "type": "array"}, "defaultMaxReadPerRequest": {"type": "number"}, "defaultMaxSearchPerRequest": {"type": "number"}, "nextSearchMax": {"type": "number"}, "nextSearchMin": {"type": "number"}, "readTimeMax": {"type": "number"}, "readTimeMin": {"type": "number"}, "scrollRandomResults": {"type": "boolean"}, "searchLimits": {"items": {"properties": {"count": {"type": "number"}, "maxCount": {"type": "number"}, "maxScore": {"type": "number"}, "minScore": {"type": "number"}}, "type": "object"}, "type": "array"}}, "type": "object"}, "serverId": {"type": "string"}}, "type": "object"}