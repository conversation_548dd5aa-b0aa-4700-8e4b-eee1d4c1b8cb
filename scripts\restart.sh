#!/bin/bash
# 检查内存可用情况的脚本

export HEADLESS=true
export SESSION_DIR=$HOME/sessions
export CACHE_DIR=$HOME/cache
export LOG_DIR=$HOME/logs
export NO_SLEEP=true

# 确保脚本以root权限运行
if [ $(id -u) -ne 0 ]; then
    echo "This script must be run as root"
    exit 1
fi

# 设置可用内存最小阈值（例如，10%）
MIN_FREE_THRESHOLD=5

# 获取内存信息
TOTAL_MEMORY=$(free | grep Mem | awk '{print $2}')
FREE_MEMORY=$(free | grep Mem | awk '{print $4}')

# 计算可用内存百分比
FREE_MEMORY_PERCENT=$((FREE_MEMORY * 100 / TOTAL_MEMORY))

# 如果可用内存低于阈值，则执行指定的脚本
if [ $FREE_MEMORY_PERCENT -lt $MIN_FREE_THRESHOLD ]; then

    # 同步文件系统缓冲区
    sync

    # 清理缓存
    echo 3 > /proc/sys/vm/drop_caches

    # 记录清理时间
    echo "Cache cleared at $(date)"

    echo "可用内存低于 $MIN_FREE_THRESHOLD%，执行指定脚本"
    # 替换为你要执行的脚本路径
    $HOME/workspace/microsoft-rewards-script/scripts/run_daily.sh > $HOME/logs/mrsa-hour-$(date +\%H).log 2>&1
else
    # echo "内存使用正常，当前可用内存: $FREE_MEMORY_PERCENT%"
    exit 1
fi