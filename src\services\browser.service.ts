import { Browser, chromium, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Page, LaunchOptions, devices } from 'playwright';
import { newInjectedContext } from 'fingerprint-injector'
import { SessionService } from './session.service.js';
import { Config } from './config.service.js';
import { Logger } from './logger.service.js';
import { ProxyAgent, Agent, setGlobalDispatcher, fetch } from 'undici';
import fs from 'fs/promises';
import path from 'path';
import { OperatingSystem, Device } from 'header-generator';
//浏览器的创建和上下文的管理
export class BrowserService {
  private static instance: BrowserService;
  private pcBrowser?: Browser;
  private mobileBrowser?: Browser;
  private cacheEnabled: boolean = false;
  private cachePath: string = '';
  private cacheTimeToLive: number = 0; // 缓存有效期（毫秒）
  private constructor() { }

  public deviceName: string = "Desktop Edge";

  public static getInstance(): BrowserService {
    if (!BrowserService.instance) {
      BrowserService.instance = new BrowserService();
    }
    return BrowserService.instance;
  }

  async initBrowsers() {
    const config = Config.getInstance().getConfig();
    const browserConfig = config.browser;
    const launchOptions: LaunchOptions = {
      headless: browserConfig.headless,
      ...(browserConfig.proxy?.server && {
        proxy: {
          server: `${browserConfig.proxy.server}`,
          username: browserConfig.proxy.username,
          password: browserConfig.proxy.password
        }
      }),
      args: [
        '--no-sandbox',
        '--mute-audio',
        '--disable-setuid-sandbox',
        '--disable-webgl',
        '--ignore-certificate-errors',
        '--ignore-certificate-errors-spki-list',
        '--ignore-ssl-errors'
      ]
    };


    // 配置资源缓存
    if (browserConfig.cache?.enable) {
      this.cacheEnabled = true;
      this.cachePath = browserConfig.cache.path;
      // 设置缓存有效期，默认为24小时
      this.cacheTimeToLive = browserConfig.cache.timeToLive > 0
        ? browserConfig.cache.timeToLive * 60 * 60 * 1000
        : 24 * 60 * 60 * 1000; // 默认24小时

      // 为PC浏览器和移动浏览器分别创建缓存目录
      const pcCachePath = path.join(this.cachePath, 'pc');
      const mobileCachePath = path.join(this.cachePath, 'mobile');

      // 确保缓存目录存在
      await this.ensureCacheDirectoryExists(pcCachePath);
      await this.ensureCacheDirectoryExists(mobileCachePath);
      // 清理过期缓存
      await this.cleanExpiredCache(this.cachePath);

      Logger.getInstance().info(`浏览器资源缓存已启用，路径`, ` ${this.cachePath}`);
      Logger.getInstance().info(`缓存过期时间`, ` ${browserConfig.cache.timeToLive}小时`);

      // 设置缓存大小，默认500MB
      launchOptions.args!.push('--disk-cache-size=524288000');
      // 启用缓存
      launchOptions.args!.push('--aggressive-cache-discard');
      launchOptions.args!.push('--enable-aggressive-domstorage-flushing');

      this.pcBrowser = await chromium.launch({
        ...launchOptions,
        args: [
          ...(launchOptions.args || []),
          `--disk-cache-dir=${pcCachePath}`,
        ]
      });
      this.mobileBrowser = await chromium.launch({
        ...launchOptions,
        args: [
          ...(launchOptions.args || []),
          `--disk-cache-dir=${mobileCachePath}`,
        ]
      });
    }
    else {
      this.pcBrowser = await chromium.launch(launchOptions);
      this.mobileBrowser = await chromium.launch(launchOptions);
    }
    try {
      const response = await fetch('https://httpbin.org/ip');
      const data: any = await response.json();
      Logger.getInstance().info('API IP地址(代理)', data.origin);
    } catch (error) {
      Logger.getInstance().info('API IP测试失败', `${error}`);
    }


    try {
      //测试的时候注意把tun模式关闭..不然会和代理一样
      const response = await fetch('https://httpbin.org/ip', {
        dispatcher: new Agent() // 创建新的默认 Agent 实例
      });
      const data: any = await response.json();
      Logger.getInstance().info('API IP地址(直连)', data.origin);
    } catch (error) {
      Logger.getInstance().info('API IP测试失败', `${error}`);
    }
  }

  public async checkIPInfo(context:BrowserContext): Promise<void> {
    const page = await context.newPage();
    try {
      //https://ping0.cc/geo  https://myip.ipip.net/
      await page.goto('https://ping0.cc/geo', { waitUntil: 'load', timeout: 60000 });
      const textContent = await page.textContent("body");
      if (textContent) {
        Logger.getInstance().info('浏览器的IP地址', `\n${textContent}`);
        const config = Config.getInstance().getConfig();
        const browserConfig = config.browser;
        // 检查IP地区
        if (browserConfig.region && browserConfig.region.trim() && browserConfig.region.trim() !== '') {
          const requiredRegion = browserConfig.region.trim().toLowerCase();
          const actualRegion = textContent.toLowerCase();
          if (!actualRegion.includes(requiredRegion)) {
            throw new Error(`IP地区检查失败: 当前IP地区信息不包含配置的目标地区"${browserConfig.region}"`)
          } else {
            Logger.getInstance().info('IP地区检查通过',
              `当前IP地区信息包含目标地区"${browserConfig.region}"`);
          }
        }
      }
    }
    finally {
      page.close();
    }
  }

  // 使用 Node.js 的 process.platform 检测当前操作系统
  private getOperatingSystem(): OperatingSystem {
    return "windows";
    switch (process.platform) {
      case 'win32':
        return "windows";
      case 'darwin':
        return "macos";
      case 'linux':
        return "linux";
      default:
        // 如果是其他平台且检测到是移动设备，可以增加移动设备检测逻辑
        // 这里默认返回 windows 作为后备选项
        return "windows";
    }
  }

  async createBrowserContext(id: string, isMobile: boolean) {

    const browser = isMobile ? this.mobileBrowser : this.pcBrowser;
    if (!browser) {
      throw new Error('Browsers are not initialized');
    }
    const sessionService = SessionService.getInstance();
    const cookies = await sessionService.loadSessionData(id, isMobile);
    // 随机选择操作系统类型

    // Randomly select operating system based on device type
    // 定义操作系统类型
    type OperatingSystem = "windows" | "macos" | "linux" | "android" | "ios";

    // 使用当前日期作为随机种子
    const today = new Date();
    const day = today.getDate(); // 获取日期（1-31）
    const month = today.getMonth() + 1; // 获取月份（1-12）
    const seed = month; //day + month  // 简单的种子值 用月份就是每个月换个操作系统.加上天数就是每天

    // 操作系统列表
    const desktopOS: OperatingSystem[] = [this.getOperatingSystem()];//["windows","macos", "linux"]; 
    const mobileOS: OperatingSystem[] = ["android", "ios"];

    // 根据设备类型和日期随机选择操作系统
    let operatingSystem: OperatingSystem;

    if (isMobile) {
      // 移动设备选择
      const index = seed % mobileOS.length;
      operatingSystem = mobileOS[index];
      //  operatingSystem = Math.random() > 0.5 ? "android" : "ios"; ///纯随机
    } else {
      // 桌面设备选择
      const index = seed % desktopOS.length;
      //const index = Math.floor(Math.random() * desktopOS.length); //纯随机
      operatingSystem = desktopOS[index];
    }
    // Load fingerprint with the selected operating system
    const fingerprint = await sessionService.loadFingerprint(id, operatingSystem);

    this.deviceName = "Desktop Chrome";
    if (operatingSystem == "android") {
      // 安卓设备选项
      const androidDevices = [
        'Pixel 7',
        'Galaxy S8',
        'Galaxy S9+',
        'Galaxy Tab S4'
      ];
      this.deviceName = androidDevices[Math.floor(Math.random() * androidDevices.length)];
    }
    else if (operatingSystem == "ios") {
      // iOS设备选项
      const iosDevices = [
        'iPhone 15',
        'iPhone 15 Pro',
        'iPhone 15 Pro Max',
        'iPad Pro 11'
      ];
      this.deviceName = iosDevices[Math.floor(Math.random() * iosDevices.length)];
    }
    //Logger.getInstance().info(`设备信息 [${this.deviceName}]`, JSON.stringify(device))
    //Logger.getInstance().info(`设备信息 [${this.deviceName}]`, JSON.stringify(fingerprint.fingerprint.navigator))
    //如果发现移动端的搜索不能用..页面是乱的(看起来像是css或者js没有加载完整造成,实际原因可能和设备的指纹有关,比如指纹和浏览器上下文设置的设备相冲突)
    //Error:  TimeoutError: locator.click: Timeout 5000ms exceeded. waiting for locator('#sb_form_q')
    const context: BrowserContext = await newInjectedContext(browser, {
      fingerprint,
      newContextOptions: {
        //...(isMobile ? {} : devices[this.deviceName]),  //指纹本身就设置了设备,移动端的话使用指纹的设备,桌面的还是使用自定义的设备
        locale: 'zh-CN',
        timezoneId: 'Asia/Shanghai',
        serviceWorkers: 'allow', // 允许Service Workers (有助于缓存)
        javaScriptEnabled: true,
        bypassCSP: false // 不绕过内容安全策略
      }
    });
    const browserConfig = Config.getInstance().getConfig().browser;
    // 设置所有操作的默认超时时间为 30 秒,等待元素跳转等操作的最大时间
    context.setDefaultTimeout(browserConfig.globalTimeout);
    await context.addCookies(cookies);
    // 在新页面创建时启用缓存
    if (this.cacheEnabled) {
      context.on('page', async (page) => {
        await this.setupPageCaching(page);
      });
    }
    return context;
  }

  // 通用页面打开验证函数
  // 写这个的原因,是因为打开页面超时会导致整个程序奔溃,可能是playwright的bug
  static async safeOpenPage(openerPage: Page, triggerAction: () => Promise<void>, timeout = 10000): Promise<Page> {
    const initialPages = openerPage.context().pages();
    try {
      await triggerAction();
      const newPage = await Promise.race([
        openerPage.context().waitForEvent('page'),
        (async () => {
          let elapsed = 0;
          while (elapsed < timeout) {
            const currentPages = openerPage.context().pages();
            const target = currentPages.find(p => !initialPages.includes(p));
            if (target) return target;
            await openerPage.waitForTimeout(500);
            elapsed += 500;
          }
          throw new Error(`Page opening timeout after ${timeout}ms`);
        })()
      ]);
      return newPage;
    }
    catch (error) {
      // 清理可能创建的新页面
      const currentPages = openerPage.context().pages();
      const newPages = currentPages.filter(p => !initialPages.includes(p));
      await Promise.all(newPages.map(p => p.close().catch(() => { })));
      throw new Error(`Failed to safely open new page: ${error}`);
    }
  }


  /**
   * 确保缓存目录存在
   */
  private async ensureCacheDirectoryExists(cachePath: string): Promise<void> {
    try {
      await fs.access(cachePath);
    } catch (error) {
      // 目录不存在，创建它
      await fs.mkdir(cachePath, { recursive: true });
      Logger.getInstance().info(`已创建缓存目录`, `${cachePath}`);
    }
  }
  // 检查文件或目录是否存在
  private async exists(path: string): Promise<boolean> {
    try {
      await fs.access(path);
      return true;
    } catch {
      return false;
    }
  }
  // 清理过期缓存
  private async cleanExpiredCache(cachePath: string) {
    try {
      // 创建缓存元数据文件路径
      const metaFilePath = path.join(cachePath, 'cache_metadata.json');

      // 如果元数据文件存在，读取并检查过期项
      if (await this.exists(metaFilePath)) {
        const metadataContent = await fs.readFile(metaFilePath, 'utf8');
        const metadata = JSON.parse(metadataContent);
        const now = Date.now();
        let hasExpired = false;

        // 检查是否有缓存过期
        if (metadata.lastCleanup && (now - metadata.lastCleanup > this.cacheTimeToLive)) {
          // 如果上次清理时间已经过期，则清空整个缓存目录（除了元数据文件）
          const files = await fs.readdir(cachePath);
          for (const file of files) {
            if (file !== 'cache_metadata.json') {
              const filePath = path.join(cachePath, file);
              const stat = await fs.stat(filePath);
              if (stat.isDirectory()) {
                await fs.rm(filePath, { recursive: true, force: true });
              } else {
                await fs.unlink(filePath);
              }
            }
          }
          hasExpired = true;
          Logger.getInstance().info(`清理过期缓存`, `${cachePath}`);
        }

        // 更新元数据
        if (hasExpired) {
          await fs.writeFile(metaFilePath, JSON.stringify({ lastCleanup: now }));
        }
      } else {
        // 如果元数据文件不存在，创建一个
        await fs.writeFile(metaFilePath, JSON.stringify({ lastCleanup: Date.now() }));
      }
    } catch (error) {
      Logger.getInstance().error(`清理缓存失败`, `${error}`);
    }
  }
  /**
   * 为页面设置缓存优化
   */
  private async setupPageCaching(page: Page): Promise<void> {
    // 使用正则表达式匹配所有需要缓存的静态资源类型
    const staticResourceRegex = /\.(js|css|mjs|jsx|ts|tsx|jpg|jpeg|png|gif|svg|webp|avif|ico|bmp|tiff|woff|woff2|ttf|otf|eot|mp3|mp4|webm|ogg|wav|pdf|json|xml|map)(\?.*)?$/;
    //glob 模式匹配，通常用于文件系统路径匹配
    await page.route('**/*.{js,css,mjs,jsx,ts,tsx,jpg,jpeg,png,gif,svg,webp,avif,ico,bmp,tiff,woff,woff2,ttf,otf,eot,mp3,mp4,webm,ogg,wav,pdf,json,xml,map}', async (route) => {
      const url = route.request().url();
      const resourceType = route.request().resourceType();
      try {
        // 允许资源请求通过，但添加缓存控制头
        const response = await route.fetch({
          headers: {
            ...route.request().headers(),
            'Cache-Control': 'max-age=31536000', // 1年缓存
            'Pragma': 'cache'
          }
        });

        // 记录缓存的资源（可选，用于调试）
        // Logger.getInstance().debug(`缓存资源: ${resourceType} - ${url}`);

        await route.fulfill({ response });
      } catch (error) {
        // 如果缓存失败，继续原始请求
        Logger.getInstance().log(`资源缓存失败: ${url} - ${error}`);
        await route.continue();
      }
    }, { times: 0 }); // 无限次应用
  }

  async dispose() {
    if (this.pcBrowser) {
      await this.pcBrowser.close();
    }
    if (this.mobileBrowser) {
      await this.mobileBrowser.close();
    }
  }
}