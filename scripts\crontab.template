# Edit this file to introduce tasks to be run by cron.
# 
# Each task to run has to be defined through a single line
# indicating with different fields when the task will be run
# and what command to run for the task
# 
# To define the time you can provide concrete values for
# minute (m), hour (h), day of month (dom), month (mon),
# and day of week (dow) or use '*' in these fields (for 'any').
# 
# Notice that tasks will be started based on the cron's system
# daemon's notion of time and timezones.
# 
# Output of the crontab jobs (including errors) is sent through
# email to the user the crontab file belongs to (unless redirected).
# 
# For example, you can run a backup of all your user accounts
# at 5 a.m every week with:
# 0 5 * * 1 tar -zcf /var/backups/home.tgz /home/
# 
# For more information see the manual pages of crontab(5) and cron(8)
# 
# m h  dom mon dow   command

# 0 2,8,14,20 * * * HEADLESS=true SESSION_DIR=$HOME/sessions CACHE_DIR=$HOME/cache LOG_DIR=$HOME/logs /bin/bash $HOME/workspace/microsoft-rewards-script/scripts/run_daily.sh -f $HOME/deploy/microsoft-accounts.2.json > $HOME/logs/mrsa2.log 2>&1

# 每小时的0分开始执行脚本
0 * * * * HEADLESS=true SESSION_DIR=$HOME/sessions CACHE_DIR=$HOME/cache LOG_DIR=$HOME/logs /bin/bash $HOME/workspace/microsoft-rewards-script/scripts/run_daily.sh > $HOME/logs/mrsa-hour-$(date +\%H).log 2>&1

# 每30分钟开始执行脚本  0,30 表示在每小时的第0分钟和第30分钟执行
#0,30 * * * * HEADLESS=true SESSION_DIR=$HOME/sessions CACHE_DIR=$HOME/cache LOG_DIR=$HOME/logs /bin/bash $HOME/workspace/microsoft-rewards-script/scripts/run_daily.sh > $HOME/logs/mrsa-hour-$(date +\%H)$([ $(date +\%M) -ge 30 ] && echo "-2" || echo "-1").log 2>&1

# 每2小时执行一次脚本
# 0 */2 * * * HEADLESS=true SESSION_DIR=$HOME/sessions CACHE_DIR=$HOME/cache LOG_DIR=$HOME/logs /bin/bash $HOME/workspace/microsoft-rewards-script/scripts/run_daily.sh > $HOME/logs/mrsa-hour-$(date +\%H).log 2>&1

# 0-11点每小时执行一次
# 0 0-11 * * * HEADLESS=true SESSION_DIR=$HOME/sessions CACHE_DIR=$HOME/cache LOG_DIR=$HOME/logs /bin/bash $HOME/workspace/microsoft-rewards-script/scripts/run_daily.sh > $HOME/logs/mrsa-hour-$(date +\%H).log 2>&1

# 12-23点每30分钟执行一次 0,30 表示在每小时的第0分钟和第30分钟执行
# 0,30 12-23 * * * HEADLESS=true SESSION_DIR=$HOME/sessions CACHE_DIR=$HOME/cache LOG_DIR=$HOME/logs /bin/bash $HOME/workspace/microsoft-rewards-script/scripts/run_daily.sh > $HOME/logs/mrsa-hour-$(date +\%H).log 2>&1

# 每天的10点优选dns
0 10 * * * /bin/bash $HOME/workspace/microsoft-rewards-script/scripts/ping.sh > $HOME/logs/ping.log 2>&1

# 每天的0点10分备份本机账号
# 10 0 * * *  /bin/bash $HOME/workspace/microsoft-rewards-script/scripts/backup.sh > $HOME/logs/backup.log 2>&1 

# 每小时的10,40分钟通知一次
#10,40 * * * *  LOG_DIR=$HOME/logs /bin/bash $HOME/workspace/microsoft-rewards-script/scripts/log.sh > $HOME/logs/remotelog.log 2>&1 

# 每10分钟通知一次
*/10 * * * * LOG_DIR=$HOME/logs /bin/bash $HOME/workspace/microsoft-rewards-script/scripts/log.sh > $HOME/logs/remotelog.log 2>&1 

# 每隔2分钟检查一次内存 nice -n <优先级> <命令> 调整进程的优先级
*/2 * * * * nice -n -20 /bin/bash $HOME/workspace/microsoft-rewards-script/scripts/restart.sh >> $HOME/logs/restart.log 2>&1



