- name: My first play
  hosts: hostgroup
  remote_user: root
  tasks:
    - name: 复制到远程服务器
      copy:
        src: archive/release.tar.gz
        dest: /root/deploy/microsoft-rewards-script.tar.gz

    - name: 创建临时目录用于解压
      file:
        path: /tmp/ms-rewards-new
        state: directory
        mode: '0755'

    - name: 解压文件到临时目录
      unarchive:
        src: /root/deploy/microsoft-rewards-script.tar.gz
        dest: /tmp/ms-rewards-new
        remote_src: yes

    - name: 确保目标目录存在
      file:
        path: /root/workspace/microsoft-rewards-script
        state: directory
        mode: '0755'

    - name: 优雅地更新文件（使用rsync命令替代synchronize模块）
      shell: |
        rsync -a --update /tmp/ms-rewards-new/ /root/workspace/microsoft-rewards-script/
      args:
        executable: /bin/bash

    - name: 清理临时目录
      file:
        path: /tmp/ms-rewards-new
        state: absent

    - name: 构建
      shell: |
        cd /root/workspace/microsoft-rewards-script
        source /root/.nvm/nvm.sh
        nvm use v23.0.0 
        npm install
        npm run build
        echo "[]" > /root/workspace/microsoft-rewards-script/dist/accounts.json
      args:
        executable: /bin/bash
 
    - name: 运行脚本
      shell: |
        mkdir -p /root/sessions
        mkdir -p /root/cache
        mkdir -p /root/logs
        chmod +x /root/workspace/microsoft-rewards-script/scripts/*.sh
        /root/workspace/microsoft-rewards-script/scripts/setup-crontab.sh
      args:
        executable: /bin/bash

    # 异步执行 playwright install
    # 另外的方法   nohup npx playwright install chromium > /dev/null 2>&1 &
    - name: 异步安装 Playwright Chromium
      shell: |
        cd /root/workspace/microsoft-rewards-script
        source /root/.nvm/nvm.sh
        nvm use v23.0.0
        nohup npx playwright install chromium > /dev/null 2>&1 &
      args:
        executable: /bin/bash
      async: 3600  # 最长运行时间（秒）
      poll: 0      # 0表示立即返回，不等待任务完成

