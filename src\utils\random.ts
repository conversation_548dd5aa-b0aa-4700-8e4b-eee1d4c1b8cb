export function shuffleArray<T>(array: T[], seed?: number): T[] {
    let currentSeed: number;

    // 初始化种子，确保为非零整数
    if (seed !== undefined) {
        if (!Number.isInteger(seed)) {
            throw new Error("Seed must be an integer."); // 种子必须是整数
        }
        // LCG 的种子通常不应为0。这里的处理是如果传入0，或者 abs(seed) % M === 0，则用1。
        // 2147483647 是梅森素数 M_31 = 2^31 - 1，常用于 LCG 的模数。
        currentSeed = Math.abs(seed) % 2147483647;
        if (currentSeed === 0 && seed !== 0) { // 如果 abs(seed) 是 2147483647 的倍数但 seed 本身不是0
            currentSeed = 1; // 避免因取模导致合法非零种子变为0，然后被下面的 || 1 捕获
        }
        currentSeed = currentSeed || 1; // 如果 seed 是 0 或 abs(seed) % M 是 0, 则种子为 1
    } else {
        // 如果没有提供种子，生成一个 [1, 2147483646] 之间的随机整数种子
        currentSeed = Math.floor(Math.random() * (2147483647 - 1)) + 1;
    }

    // 伪随机数生成器 (PRNG)，这是一个 Park-Miller LCG (MINSTD)
    // X_n+1 = (a * X_n) mod m
    // a = 16807, m = 2147483647
    const seededRandom = () => {
        currentSeed = (currentSeed * 16807) % 2147483647;
        // 将结果标准化到 [0, 1) 范围
        // currentSeed 的范围是 [1, m-1] 即 [1, 2147483646]
        // (currentSeed - 1) 的范围是 [0, 2147483645]
        // 除以 (m-1) 即 2147483646
        return (currentSeed - 1) / (2147483647 - 1);
    };

    const newArray = [...array]; // 创建数组副本，不修改原数组
    // Fisher-Yates 洗牌算法
    for (let i = newArray.length - 1; i > 0; i--) {
        // 生成一个 [0, i] 范围内的随机索引 j
        const j = Math.floor(seededRandom() * (i + 1));
        // 交换元素 newArray[i] 和 newArray[j]
        [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
    }
    return newArray;
}

export function getRandomInRange(min: number, max: number): number {
    // Math.random() 生成 [0, 1) 之间的随机数
    // 乘以 (max - min + 1) 并加上 min，确保结果在 [min, max] 范围内
    return Math.floor(Math.random() * (max - min + 1)) + min;
}

/**
 * 获取一年中的第几天，用于生成基于日期的随机数
 * @param date 日期对象
 * @returns 一年中的第几天 (1-365/366)
 */
export function getDayOfYear(date: Date): number {
  const start = new Date(date.getFullYear(), 0, 0);
  const diff = date.getTime() - start.getTime();
  const oneDay = 1000 * 60 * 60 * 24;
  return Math.floor(diff / oneDay);
}

/**
 * 基于种子生成伪随机数
 * @param min 最小值（包含）
 * @param max 最大值（包含）
 * @param seed 随机种子
 * @returns 在指定范围内的随机整数
 */
export function seededRandom(min: number, max: number, seed: number): number {
  // 简单的伪随机数生成器，基于种子值
  const x = Math.sin(seed) * 10000;
  const randomValue = x - Math.floor(x); // 生成 0-1 之间的数字
  return Math.floor(randomValue * (max - min + 1)) + min;
}

export function weightShuffleArray<T extends { weight?: number }>(array: T[]): T[] {
    if (array.length === 0) return [];

    // 如果所有项的权重都为 0 或 undefined，则使用普通的随机排序
    const allZeroWeight = array.every(item => !item.weight);
    if (allZeroWeight) {
        return shuffleArray(array);
    }

    const result: T[] = [];
    let remainingItems = [...array];

    while (remainingItems.length > 0) {
        // 计算剩余项的总权重
        const totalWeight = remainingItems.reduce((sum, item) => sum + (item.weight || 0), 0);

        // 生成一个 0 到总权重之间的随机数
        const randomWeight = Math.random() * totalWeight;

        let currentWeight = 0;
        let selectedIndex = -1;

        // 遍历找到被选中的项
        for (let i = 0; i < remainingItems.length; i++) {
            currentWeight += remainingItems[i].weight || 0;
            if (randomWeight <= currentWeight) {
                selectedIndex = i;
                break;
            }
        }

        // 如果没有找到（可能由于浮点数精度问题），选择最后一项
        if (selectedIndex === -1) {
            selectedIndex = remainingItems.length - 1;
        }

        // 将选中的项添加到结果数组，并从剩余项中移除
        result.push(remainingItems[selectedIndex]);
        remainingItems.splice(selectedIndex, 1);
    }

    return result;
}
