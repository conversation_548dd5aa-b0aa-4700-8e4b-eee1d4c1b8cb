{
    "$schema": "./schema.json",
    "startHour": "0", // 程序开始执行的小时（0-23）
    "serverId": "",
    "browser": {
        "globalTimeout": 30000,
        "headless": false,
        "region": "",
        "sessionDir": "./sessions",
        "fingerprintExpirationDays": 7,
        "proxy": {
            "server": "",
            "username": "",
            "password": ""
        },
        "cache": {
            "path": "./cache",
            "enable": true,
            "timeToLive": 240
        }
    },
    "log": {
        "localDir": "./logs",
        "saveDays": 7
    },
    "account": {
        "apiUrl": "http://159.138.99.139:91", //https://seedlog.igiven.com/account/get
        "apiToken": "",
        "distributionMode": "all",
        "distributionCycleDays": 2,
        "runningCount": 1, //计数模式(根据每天执行的次数和分钟数,计算出分组,每组间隔runningSlotMinutes交替执行)
        "runningSlotMinutes": 60,
        "runningGroup": 2, //分组模式(分组,将账号分组每组执行24/runningGroup小时,每组间隔runningSlotMinutes执行一次)
        "runningMode": "count",
        "dailyTasksSeparately": false,
        "dailyTasksHour": -1,
        "execLimits": [
            {
                "minScore": 0,
                "maxScore": 3000,
                "maxDailyExecutionLimit": 1
            }
        ]
    },
    "proof": [
        {
            "suffix": "godgodgame.com",
            "apiUrl": "http://159.138.99.139:89/api/latest-email" //https://seedmail.igiven.com/api/latest-email
        },
        {
            "suffix": "igiven.com",
            "apiUrl": "http://159.138.99.139:90/api/latest-email" //https://mail.igiven.com/api/latest-email
        }
    ],
    "reward": {
        "country": "cn",
        "language": "zh-hans",
        "doWaitMin": 0,
        "doWaitMax": 10
    },
    "search": {
        "apiUrl": "http://159.138.99.139:88/api", //https://headline.igiven.com/api
        "items": [
            "baidu",
            "douyin",
            "toutiao",
            "weibo",
            "tieba",
            "sina"
        ],
        "readTimeMin": 10,
        "readTimeMax": 15,
        "nextSearchMin": 10,
        "nextSearchMax": 20,
        "scrollRandomResults": true,
        "clickRandomResults": true,
        "defaultMaxSearchPerRequest": 3, // 每次搜索的最大数量 因为微软的限制..超过3次也不能增加积分
        "defaultMaxReadPerRequest": 1,
        "searchLimits": [
            {
                "minScore": 0,
                "maxScore": 3000,
                "maxSearchPerRequest": 0,
                "maxDailySearchLimit": 0,
                "maxReadPerRequest": 0,
                "maxDailyReadLimit": 0
            },
            {
                "minScore": 3000,
                "maxScore": 6000,
                "maxSearchPerRequest": 2,
                "maxDailySearchLimit": 25,
                "maxReadPerRequest": 1,
                "maxDailyReadLimit": 10
            },
            {
                "minScore": 6000,
                "maxScore": 100000,
                "maxSearchPerRequest": 3, // 每次搜索的最大数量 因为微软的限制..超过3次也不能增加积分
                "maxDailySearchLimit": 50,
                "maxReadPerRequest": 2, // 1次也可以,但是为了保证积分读满,还是2次
                "maxDailyReadLimit": 10
            }
            // {
            //     "minScore": 17000,
            //     "maxScore": 18000,
            //     "maxSearchPerRequest": 1,
            //     "maxDailySearchLimit": 15,
            //     "maxReadPerRequest": 1,
            //     "maxDailyReadLimit": 10
            // }
        ]
    }
}