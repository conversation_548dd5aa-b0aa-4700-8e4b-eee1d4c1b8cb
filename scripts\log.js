import * as fs from 'fs';
import path from 'path';
import os from 'os';
import dotenv from 'dotenv';
import moment from 'moment-timezone';
import { fileURLToPath } from 'url';

const homeDir = os.homedir() || process.env.HOME || process.env.USERPROFILE || "";
const currentDir = process.cwd();
const entryDir = path.dirname(path.dirname(fileURLToPath(import.meta.url)));

//dotenv.config(); // 加载项目根目录的 .env 文件

// 定义搜索路径顺序
const envPaths = [
    path.join(entryDir, '.env'),
    path.join(entryDir, '.env.local'),
    path.join(currentDir, '.env'),
    path.join(currentDir, '.env.local'),
    path.join(homeDir, 'deploy', '.env'),
    path.join(homeDir, 'deploy', '.env.local'),
    path.join(homeDir, '.env'),
    path.join(homeDir, '.env.local'),
];

let loadedAnyFile = false;
// 遍历所有路径并加载存在的 .env 文件
for (const envPath of envPaths) {
    if (fs.existsSync(envPath)) {
        try {
            // 使用 override: true 以允许后面的文件覆盖前面的环境变量.允许覆盖命令行参数
            dotenv.config({
                path: envPath,
                override: true
            });
            console.log(`成功加载环境文件: ${envPath}`);
            loadedAnyFile = true;
        } catch (error) {
            console.error(`加载环境文件 ${envPath} 时出错:`, error);
        }
    }
    else {
        console.log(`环境文件不存在: ${envPath}`);
    }
}

const logPath = process.argv[2] || process.env.LOG_DIR ||  path.join(currentDir,'logs');
const SERVER_ID = process.argv[3] || process.env.SERVER_ID;
const LOG_API_TOKEN = process.argv[4] || process.env.LOG_API_TOKEN;
async function remote(logPath, server) {
    try {
        // 读取所有匹配的文件
        const datetime = moment().tz('Asia/Shanghai').format('YYYY-MM-DD');

        const filePath = path.join(logPath, `error-${datetime}.log`);

        console.log('errorlogPath:', filePath);

        const content =  fs.readFileSync(filePath, 'utf-8');

        // 上传到 Cloudflare KV，添加前缀
        const response = await fetch('http://159.138.99.139:91/log/post', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${LOG_API_TOKEN}`
            },
            body: JSON.stringify({
                server: server,
                service: 'msr-error',
                datetime: datetime,
                log: content
            })
        });
        if (!response.ok) {
            throw new Error(`Failed to backup ${filePath}: ${response.statusText}`);
        }
        console.log(`Successfully log  ${filePath}`);
    } catch (error) {
        console.error('Backup failed:', error);
    }


    try {
        // 读取所有匹配的文件
        const datetime = moment().tz('Asia/Shanghai').format('YYYY-MM-DD');
        const filePath = path.join(logPath, `important-${datetime}.log`);
        console.log('userlogPath:', filePath);
        const content = fs.readFileSync(filePath, 'utf-8');
        // 上传到 Cloudflare KV，添加前缀
        const response = await fetch('http://159.138.99.139:91/log/post', {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Authorization': `Bearer ${LOG_API_TOKEN}`
            },
            body: JSON.stringify({
                server: server,
                service: 'msr-user',
                datetime: datetime,
                log: content
            })
        });

        if (!response.ok) {
            throw new Error(`Failed to backup ${filePath}: ${response.statusText}`);
        }
        console.log(`Successfully log  ${filePath}`);
    } catch (error) {
        console.error('Backup failed:', error);
    }
}



// 执行备份，传入部署路径和前缀（可选）
remote(logPath,SERVER_ID );