import * as readline from 'node:readline/promises';
import { stdin as input, stdout as output } from 'node:process';

interface WaitOptions {
  timeout?: number;
  prompt?: string;
}

export async function debug_pause(message: string, options: WaitOptions = {}) {
  const { timeout = 0, prompt = '按回车继续...' } = options;
  const rl = readline.createInterface({ input, output });
  
  try {
    const promise = rl.question(`${message}\n${prompt}`);
    
    if (timeout > 0) {
      const timeoutPromise = new Promise((_resolve, reject) => {
        setTimeout(() => reject(new Error('等待超时')), timeout);
      });
      
      await Promise.race([promise, timeoutPromise]);
    } else {
      await promise;
    }
  } finally {
    rl.close();
  }
}