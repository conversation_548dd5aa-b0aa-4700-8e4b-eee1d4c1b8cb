import { Page } from 'playwright';
import { Config } from './config.service.js';
import { Logger } from './logger.service.js';
import { AccountInfo, ProofConfig } from '../types/index.js';
import { debug_pause } from '../utils/debug.js';
import { ProxyAgent, Agent, setGlobalDispatcher, fetch } from 'undici';
//定义新的错误类
export class AccountLockedError extends Error {
    constructor(message: string) {
        super(message);
        this.name = 'AccountLockedError';
    }
}

export class AccountAbuseError extends Error {
    constructor(message: string) {
        super(message);
        this.name = 'AccountAbuseError';
    }
}

//负责用户的登录
export class LoginService {
    private static instance: LoginService;
    private config = Config.getInstance().getConfig();
    private logger = Logger.getInstance();
    private directAgent = new Agent();
    private constructor() { }

    public static getInstance(): LoginService {
        if (!LoginService.instance) {
            LoginService.instance = new LoginService();
        }
        return LoginService.instance;
    }

    private getProofConfig(email: string): ProofConfig {
        const suffix = email.substring(email.indexOf('@') + 1);
        const proofConfig = this.config.proof.find(p => p.suffix === suffix);
        if (!proofConfig || !proofConfig.token) {
            throw new Error(`No valid proof configuration found for ${suffix}`);
        }
        return proofConfig;
    }

    //登录后的页面
    private async checkLoginStatus(page: Page, account: AccountInfo): Promise<boolean> {
        try {
            await page.waitForURL('https://rewards.bing.com', { timeout: 30000 });
        } catch (error) {
            this.logger.info(account.email, `可能没有登录${page.url()}: ${error}`);
        }

        try {
            await page.waitForSelector('.maybe-later', { timeout: 3000 });
            await page.click('.maybe-later')
        } catch (error) {
            this.logger.info(account.email, `没有必应应用遮罩，继续执行: ${error}`);
        }

        const isLocked = await page.waitForSelector('#rewards-user-suspended-error', { state: 'visible', timeout: 1000 }).then(() => true).catch(() => false)
        if (isLocked) {
            this.logger.important(account.email, "账号已被锁定")
            throw new AccountLockedError('账号已被锁定!')
        }
        this.logger.info(account.email, `账号没有被锁定`);
        let isLoggedIn = false;
        try {
            await page.waitForSelector('html[data-role-name="RewardsPortal"]', { timeout: 3000 })
            isLoggedIn = true;
        }
        catch (error) {
            this.logger.info(account.email, `${page.url()}没有找到RewardsPortal,登录失败: ${error}`);
        }
        if (isLoggedIn) {
            try {
                // 等待容器元素加载（最多等待5秒）
                await page.locator('#user-warning-container').waitFor({
                    timeout: 3000
                });
                // 获取具体的警告文本 
                const warningText = await page.locator('#user-warning-container span.ng-binding')
                    .first()
                    .textContent();
                this.logger.error(
                    account.email,
                    `检测到用户警告: ${warningText?.trim() || '无具体警告信息'}`
                );
            } catch (error) {
                this.logger.info(account.email, `没有用户警告，继续执行: ${error}`);
            }

            try {
                const cookieConsent = page.locator('#cookieConsentContainer');
                if (await cookieConsent.isVisible({ timeout: 1000 })) {
                    // 点击"接受"按钮（第一个按钮）
                    const acceptButton = cookieConsent.locator('button').first();
                    await acceptButton.click();
                    await page.waitForTimeout(1000);
                }
            } catch (error) {
                this.logger.info(account.email, `处理 cookie 同意弹窗失败: ${error}`);
            }

            //检查连续保护的弹窗是否存在
            try {
                // 检查 .dashboardPopUpModal 是否存在
                const popupExists = await page.locator('mee-rewards-pop-up[promotion="$ctrl.streakPopUpPromotion"] .dashboardPopUpModal').isVisible({ timeout: 3000 });
                console.log(`streakPopUpPromotion弹窗是否存在${popupExists}`)
                if (popupExists) {
                    // 检查 #reward_pivot_earn 是否存在
                    const earnButton = page.locator('mee-rewards-pop-up[promotion="$ctrl.streakPopUpPromotion"] .dashboardPopUpModal #reward_pivot_earn');
                    if (await earnButton.isVisible({ timeout: 3000 })) {
                        await earnButton.click();
                    } else {
                        // 如果 earn 按钮不存在，检查关闭按钮
                        const closeButton = page.locator('mee-rewards-pop-up[promotion="$ctrl.streakPopUpPromotion"] .dashboardPopUpModal #streak-protection-popup-close-cross');
                        if (await closeButton.isVisible({ timeout: 3000 })) {
                            await closeButton.click();
                        }
                    }
                }
            } catch (error) {
                this.logger.info(account.email, `连续保护的弹窗检查，继续执行: ${error}`);
            }

            //检查优惠券的弹窗是否存在
            try {
                // 检查 .dashboardPopUpModal 是否存在
                const popupExists = await page.locator('mee-rewards-pop-up[promotion="$ctrl.couponPopUpPromotion"] .dashboardPopUpModal').isVisible({ timeout: 3000 });
                console.log(`couponPopUpPromotion弹窗是否存在${popupExists}`)
                if (popupExists) {
                    // 检查 #reward_pivot_earn 是否存在
                    const earnButton = page.locator('mee-rewards-pop-up[promotion="$ctrl.couponPopUpPromotion"] .dashboardPopUpModal #reward_pivot_earn');
                    if (await earnButton.isVisible({ timeout: 3000 })) {
                        await earnButton.click();
                    } else {
                        //右上角关闭按钮
                        const closeButton = page.locator('mee-rewards-pop-up[promotion="$ctrl.couponPopUpPromotion"] .dashboardPopUpModal #streak-protection-popup-close-cross');
                        if (await closeButton.isVisible({ timeout: 3000 })) {
                            await closeButton.click();
                        }
                    }
                }
            } catch (error) {
                this.logger.info(account.email, `积分优惠券弹窗检查，继续执行: ${error}`);
            }


            try {
                // 检查 .popUpPromotion 是否存在
                const popupExists = await page.locator('mee-rewards-pop-up[promotion="$ctrl.popUpPromotion"] .dashboardPopUpModal').isVisible({ timeout: 3000 });
                console.log(`推荐好友弹窗是否存在${popupExists}`)
                if (popupExists) {
                    // 检查 #reward_pivot_earn 是否存在
                    const earnButton = page.locator('mee-rewards-pop-up[promotion="$ctrl.popUpPromotion"] .dashboardPopUpModal .dashboardPopUpPopUpCloseButton');
                    if (await earnButton.isVisible({ timeout: 3000 })) {
                        await earnButton.click();
                    } else {
                        //右上角关闭按钮
                        const closeButton = page.locator('mee-rewards-pop-up[promotion="$ctrl.popUpPromotion"] .dashboardPopUpModal #streak-protection-popup-close-cross');
                        if (await closeButton.isVisible({ timeout: 3000 })) {
                            await closeButton.click();
                        }
                    }
                }
            } catch (error) {
                this.logger.info(account.email, `推荐好友弹窗检查，继续执行: ${error}`);
            }


            this.logger.info(account.email, `账号已登录`);
            return true;
        }
        return false;
    }

    private async handleMultiFactorAuth(page: Page, account: AccountInfo) {
        // 账号需要手机验证
        let serviceAbuseLanding = false
        try {
            await page.waitForURL((url) => {
                return url.href.startsWith('https://account.live.com/Abuse');
            }, { timeout: 10000 });
            serviceAbuseLanding = true
        } catch (error) {
            this.logger.info(account.email, `没有手机解锁验证，继续执行: ${error}`);
        }

        if (serviceAbuseLanding) {
            this.logger.important(account.email, `账号需要手机验证`);
            throw new AccountAbuseError('账号需要手机验证!');
        }
        const proofEmail = account.proofEmail;

        try {
            await page.waitForURL((url) => {
                return url.href.startsWith('https://account.live.com/recover');
            }, { timeout: 3000 });
            await page.click('input[type="submit"]#iLandingViewAction');
            const timestamp = Math.floor(Date.now() / 1000);
            await page.fill("#iProofEmail", proofEmail)
            await page.click('input[type="submit"]')

            const proofConfig = this.getProofConfig(proofEmail);
            const verificationCode = await this.getVerificationCode(proofConfig.apiUrl, proofConfig.token!, proofEmail, timestamp);
            await page.fill('input[type="tel"]', verificationCode);
            await page.click('input[type="submit"]');

            //可能需要修改密码..这里就不处理了
        } catch (error) {
            this.logger.info(account.email, `没有帮助我们保护帐户确认，继续执行: ${error}`);
        }

        try {

            //新版的邮箱验证
            await page.waitForURL((url) => {
                return url.href.startsWith('https://login.live.com/oauth20_authorize.srf');
            }, { timeout: 3000 });

            const timestamp = Math.floor(Date.now() / 1000);
            await page.fill("#proof-confirmation-email-input", proofEmail)
            await page.click('button[type="submit"]')

            const proofConfig = this.getProofConfig(proofEmail);
            const verificationCode = await this.getVerificationCode(proofConfig.apiUrl, proofConfig.token!, proofEmail, timestamp);
            await page.fill('input#codeEntry-0', verificationCode[0]);
            await page.fill('input#codeEntry-1', verificationCode[1]);
            await page.fill('input#codeEntry-2', verificationCode[2]);
            await page.fill('input#codeEntry-3', verificationCode[3]);
            await page.fill('input#codeEntry-4', verificationCode[4]);
            await page.fill('input#codeEntry-5', verificationCode[5]);

            //可能需要修改密码..这里就不处理了
        } catch (error) {
            this.logger.info(account.email, `没有帮助我们保护帐户确认，继续执行: ${error}`);
        }


        for (let i = 0; i < 2; i++) {
            try {
                await page.waitForURL('https://account.live.com/identity/**', { timeout: 3000 });

                try {
                    await page.waitForSelector('#iProof0', { timeout: 3000 });
                    await page.click('#iProof0')
                } catch (error) {
                    this.logger.info(account.email, `没有#iProof0，继续执行: ${error}`);
                }
                const timestamp = Math.floor(Date.now() / 1000);
                await page.fill("#iProofEmail", proofEmail)
                await page.click('input[type="submit"]')

                const proofConfig = this.getProofConfig(proofEmail);
                const verificationCode = await this.getVerificationCode(proofConfig.apiUrl, proofConfig.token!, proofEmail, timestamp);
                await page.fill('input[type="tel"]', verificationCode);
                await page.click('input[type="submit"]');
                await page.waitForTimeout(1000 * 5);
            } catch (error) {
                this.logger.info(account.email, `没有多重验证，继续执行: ${error}`);
            }
        }
    }

    // 确认登录
    async confirmLogin(page: Page, account: AccountInfo): Promise<boolean> {

        for (let i = 0; i < 2; i++) {
            try {
                await page.waitForURL('https://account.live.com/interrupt/**', { timeout: 3000 });

                // 尝试查找"暂时跳过"按钮
                const skipButtonExists = await page.isVisible('button[data-testid="secondaryButton"]', { timeout: 5000 });
                if (skipButtonExists) {
                    this.logger.info(account.email, "找到新版'暂时跳过'按钮，正在点击...");
                    await page.click('button[data-testid="secondaryButton"]');
                }

                // 最后尝试你提到的另一个按钮
                const otherButtonExists = await page.isVisible('div[data-testid="textButtonContainer"] > div:first-child > button[type="button"]', { timeout: 5000 });
                if (otherButtonExists) {
                    this.logger.info(account.email, "找到旧版'暂时跳过'按钮，正在点击...");
                    await page.click('div[data-testid="textButtonContainer"] > div:first-child > button[type="button"]');
                }
                await page.waitForTimeout(1000 * 5);
            } catch (error) {
                //暂时跳过.下一个.获取微软的通行密钥软件
                this.logger.info(account.email, `无<使用人脸、指纹或 PIN 更快地登录>，继续执行: ${error}`);
            }
        }

        try {
            //和下面一样.随机出现
            await page.waitForURL('https://login.live.com/ppsecure/**', { timeout: 3000 });
            //新版可能有问题.换成如下.
            //await page.click('#acceptButton', { timeout: 10000 });
            //await page.locator('button[type="submit"]').nth(0).click({ timeout: 10000 });
            await page.locator('button[type="submit"]').first().click({ timeout: 10000 });
        } catch (error) {
            this.logger.info(account.email, `无ppsecure登录确认，继续执行: ${error}`);
        }

        try {
            //和上面一样.随机出现
            await page.waitForURL((url) => {
                return url.href.startsWith('https://login.live.com/oauth20_authorize.srf');
            }, { timeout: 3000 });
            await page.click('button[type="submit"]', { timeout: 10000 });
        } catch (error) {
            this.logger.info(account.email, `无oauth20_authorize登录确认，继续执行: ${error}`);
        }

        const loginResult = await this.checkLoginStatus(page, account);
        return loginResult;
    }

    private async emailVerificationLogin(page: Page, account: AccountInfo, timestamp: number): Promise<boolean> {
        try {
            //邮箱验证
            const proofEmail = account.proofEmail;
            if (!proofEmail) {
                throw new Error("No proof email provided");
            }
            const proofConfig = this.getProofConfig(proofEmail);
            const verificationCode = await this.getVerificationCode(proofConfig.apiUrl, proofConfig.token!, proofEmail, timestamp);
            await page.fill('input[type="tel"]', verificationCode);
            await page.click('button[type="submit"]');
            return await this.confirmLogin(page, account);
        }
        catch (error) {
            if (error instanceof AccountLockedError) {
                throw error; // 如果是账号锁定错误，直接抛出，不再重试
            }
            this.logger.info(account.email, `邮箱验证失败: ${error}`);
        }
        return false;
    }

    private async loginAttempt(page: Page, account: AccountInfo) {
        try {
            await page.goto('https://rewards.bing.com');
        }
        catch (error) {
            this.logger.info(account.email, "loginAttempt中加载rewards.bing.com页面超时,但仍尝试继续登录");
        }

        try {
            await page.waitForURL('https://rewards.bing.com', { timeout: 10000 });
            if (await this.checkLoginStatus(page, account)) {
                this.logger.info(account.email, `cookie登录成功`);
                return;
            }
        } catch (error) {
            if (error instanceof AccountLockedError) {
                throw error; // 如果是账号锁定错误，直接抛出，不再重试
            }
            this.logger.info(account.email, `cookie没有直接登录成功: ${error}`);
        }

        //如果不是直接rewads.bing.com..那么就分为已经cookie登录和未登录两种状态
        let isCookieLogin = true;
        try {
            await page.waitForSelector("#usernameTitle", { timeout: 3000 });
            isCookieLogin = false;
        } catch (error) {
            this.logger.info(account.email, `旧版检测用户不需要重新登录: ${error}`);
        }
        try {
            await page.waitForSelector("#usernameEntry", { timeout: 3000 });
            isCookieLogin = false;
        } catch (error) {
            this.logger.info(account.email, `新版检测用户不需要重新登录: ${error}`);
        }
        if (isCookieLogin) {
            // cookie登录也会遇到email验证
            await this.handleMultiFactorAuth(page, account);
            if (await this.confirmLogin(page, account)) {
                this.logger.info(account.email, `cookie登录成功`);
                return;
            }
        }

        await page.goto("https://rewards.bing.com");
        this.logger.info(account.email, `开始账号密码登录`);

        //可能点击确定就发送了邮箱
        const timestamp = Math.floor(Date.now() / 1000);
        // 输入邮箱
        try {
            await page.waitForURL((url) => {
                return url.href.startsWith('https://login.live.com/oauth20_authorize.srf');
            }, { timeout: 3000 });
            await page.fill('input[type="email"]', account.email);
            await page.click('button[type="submit"]');
        } catch (error) {
            this.logger.info(account.email, `没有输入邮箱步骤: ${error}`);
        }

        try {
            await page.waitForSelector('#idA_PWD_SwitchToPassword', { timeout: 3000 });
            await page.click('#idA_PWD_SwitchToPassword');
        } catch (error) {
            this.logger.info(account.email, `没有旧版切换到密码登录，继续执行: ${error}`);
        }


        try {
            const passwordButtonByRole = page.getByRole('button', { name: '其他登录方法' });
            if (await passwordButtonByRole.isVisible({ timeout: 3000 })) {
                await passwordButtonByRole.click();
                await page.waitForTimeout(1000); // 等待页面稳定
            }
        } catch (error) {
            this.logger.info(account.email, `没有新版切换到其他登录方法，继续执行: ${error}`);
        }

        try {
            const passwordButtonByRole = page.getByRole('button', { name: '使用密码' });
            if (await passwordButtonByRole.isVisible({ timeout: 3000 })) {
                await passwordButtonByRole.click();
                await page.waitForTimeout(1000); // 等待页面稳定
            }
        } catch (error) {
            this.logger.info(account.email, `没有新版切换到密码登录，继续执行: ${error}`);
        }

        try {
            //直接输入邮箱验证码登录
            await page.waitForURL((url) => {
                return url.href.startsWith('https://login.live.com/oauth20_authorize.srf');
            }, { timeout: 3000 });
            await page.waitForSelector('input[type="tel"]', { timeout: 3000 });
            if (await this.emailVerificationLogin(page, account, timestamp)) {
                this.logger.info(account.email, `通过email验证登录成功`);
                return;
            }
            //点击其他登录方法
            await page.click('#idA_PWD_SwitchToCredPicker');
            //会有三个选项 1. 人脸指纹 2. 密码 3. 发送邮件
            await page.click('#tileList > div:nth-child(2) Button');
        } catch (error) {
            if (error instanceof AccountLockedError) {
                throw error; // 如果是账号锁定错误，直接抛出，不再重试
            }
            this.logger.info(account.email, `没有直接发邮件验证，继续执行: ${error}`);
        }

        try {
            // 输入密码
            await page.waitForURL((url) => {
                return url.href.startsWith('https://login.live.com/oauth20_authorize.srf');
            }, { timeout: 3000 });

            await page.fill('input[type="password"]', account.password);
            await page.click('button[type="submit"]');
        } catch (error) {
            this.logger.info(account.email, `没有输入密码步骤: ${error}`);
        }

        await this.handleMultiFactorAuth(page, account);
        if (await this.confirmLogin(page, account)) {
            this.logger.info(account.email, `账号密码登录成功`);
            return;
        }
        throw new Error("登录失败");
    }

    async login(page: Page, account: AccountInfo) {
        const maxRetries = 5;
        for (let i = 0; i < maxRetries; i++) {
            try {
                await this.loginAttempt(page, account);
                return;
            } catch (error) {
                if (error instanceof AccountLockedError) {
                    throw error; // 如果是账号锁定错误，直接抛出，不再重试
                }
                if (error instanceof AccountAbuseError) {
                    throw error; // 如果是账号需要手机验证，直接抛出，不再重试
                }
                if (i === maxRetries - 1) {
                    throw new Error(`登录失败，已重试${maxRetries}次: ${error}`);
                }
                this.logger.info(account.email, `登录失败，重试中...${error}`);
                await new Promise(resolve => setTimeout(resolve, 2000));
            }
        }
    }

    private async getVerificationCode(proofApi: string, apiKey: String, proofEmail: string, timestamp: number): Promise<string> {
        const maxRetries = 30;
        timestamp = timestamp - 5;
        this.logger.info(proofEmail, `开始获取验证码${proofApi},${apiKey},${timestamp},${new Date(timestamp * 1000)}`);
        for (let i = 0; i < maxRetries; i++) {
            try {
                const params = new URLSearchParams({
                    to: proofEmail,
                    from: '<EMAIL>',
                    timestamp: timestamp.toString()
                });

                const url = `${proofApi}?${params.toString()}`;
                const response = await fetch(url, {
                    headers: {
                        'Authorization': `Bearer ${apiKey}`
                    },
                    method: 'GET',
                    dispatcher: this.directAgent//使用直连
                });

                if (response.status === 200) {
                    const data: any = await response.json();
                    const match = data.text.match(/:\s*(\d+)\n\n/);
                    if (match) {
                        this.logger.info(proofEmail, `获取验证码成功: ${match[1]}`);
                        return match[1];
                    }
                }
                else {
                    this.logger.info(proofEmail, `获取验证码失败: ${await response.text()}`);
                }
                await new Promise(resolve => setTimeout(resolve, 2000));
            } catch (error) {
                if (i === maxRetries - 1) {
                    throw new Error("Failed to get verification code after maximum retries");
                }
            }
        }
        throw new Error("Failed to get verification code");
    }
}
