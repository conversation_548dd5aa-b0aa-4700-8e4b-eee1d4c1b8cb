import dgram from 'dgram';
import net from 'net';
import fs from 'fs';
import os from 'os';
import path from 'path';
import { exec } from 'child_process';
import readline from 'readline';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// DNS查询包构造函数
function buildDNSQuery(domain) {
    const buffer = Buffer.alloc(512);
    let offset = 0;

    // DNS Header
    const id = Math.floor(Math.random() * 65535);
    buffer.writeUInt16BE(id, 0);     // ID
    buffer.writeUInt16BE(0x0100, 2); // Flags: standard query
    buffer.writeUInt16BE(1, 4);      // Questions: 1
    buffer.writeUInt16BE(0, 6);      // Answer RRs: 0
    buffer.writeUInt16BE(0, 8);      // Authority RRs: 0
    buffer.writeUInt16BE(0, 10);     // Additional RRs: 0
    offset = 12;

    // 域名编码
    const labels = domain.split('.');
    for (const label of labels) {
        buffer.writeUInt8(label.length, offset++);
        buffer.write(label, offset);
        offset += label.length;
    }
    buffer.writeUInt8(0, offset++);  // 结束符

    // Query type (A record) and class (IN)
    buffer.writeUInt16BE(1, offset);    // Type: A
    buffer.writeUInt16BE(1, offset + 2); // Class: IN

    return buffer.slice(0, offset + 4);
}

// 解析DNS响应
function parseDNSResponse(response) {
    const ips = [];

    // 跳过Header (12字节)
    let offset = 12;

    // 跳过Query部分
    while (response[offset] !== 0) {
        offset += response[offset] + 1;
    }
    offset += 5; // 跳过结束符(1)和type(2)与class(2)

    // 跳过Question部分
    const answers = response.readUInt16BE(6);

    // 解析Answer部分
    for (let i = 0; i < answers; i++) {
        // 跳过name指针
        offset += 2;

        const type = response.readUInt16BE(offset);
        offset += 2;

        // 跳过class
        offset += 2;

        // 跳过TTL
        offset += 4;

        const dataLength = response.readUInt16BE(offset);
        offset += 2;

        // 如果是A记录(type === 1)，解析IP
        if (type === 1 && dataLength === 4) {
            const ip = `${response[offset]}.${response[offset + 1]}.${response[offset + 2]}.${response[offset + 3]}`;
            ips.push(ip);
        }

        offset += dataLength;
    }

    return ips;
}

// 直接查询DNS服务器
//Node.js 的 dns.resolve4() 默认会使用系统的 DNS 解析，这包括本地 hosts 文件。如果你想要绕过本地 hosts 文件直接查询特定的 DNS 服务器，我们可以使用 dgram 模块直接发送 DNS 查询包
function queryDNS(domain, dnsServer, port = 53) {
    return new Promise((resolve, reject) => {
        const socket = dgram.createSocket('udp4');
        const query = buildDNSQuery(domain);
        const timeout = setTimeout(() => {
            socket.close();
            reject(new Error('DNS查询超时'));
        }, 5000);

        socket.on('message', (message) => {
            clearTimeout(timeout);
            socket.close();
            try {
                const ips = parseDNSResponse(message);
                resolve(ips);
            } catch (err) {
                reject(err);
            }
        });

        socket.on('error', (err) => {
            clearTimeout(timeout);
            socket.close();
            reject(err);
        });

        socket.send(query, port, dnsServer, (err) => {
            if (err) {
                clearTimeout(timeout);
                socket.close();
                reject(err);
            }
        });
    });
}

// 优化DNS查询函数，添加重试机制
async function queryDNSWithRetry(domain, dnsServer, retries = 3) {
    for (let i = 0; i < retries; i++) {
        try {
            return await queryDNS(domain, dnsServer);
        } catch (err) {
            if (i === retries - 1) throw err;
            await new Promise(resolve => setTimeout(resolve, 1000 * i));
        }
    }
}

// 优化TCP连接测试函数，添加超时设置
async function testConnection(ip, port = 443, timeout = 5000) {
    return new Promise((resolve, reject) => {
        const startTime = Date.now();
        const socket = new net.Socket();
        
        const timeoutHandler = setTimeout(() => {
            socket.destroy();
            reject(new Error(`连接超时 (${timeout}ms)`));
        }, timeout);

        socket.connect(port, ip, () => {
            clearTimeout(timeoutHandler);
            const connectTime = Date.now() - startTime;
            socket.destroy();
            resolve(connectTime);
        });

        socket.on('error', (err) => {
            clearTimeout(timeoutHandler);
            socket.destroy();
            reject(err);
        });
    });
}

// 添加清理旧备份文件的函数
function cleanupOldBackups(hostsPath, maxBackups = 5) {
    const dir = path.dirname(hostsPath);
    const base = path.basename(hostsPath);
    
    // 获取所有备份文件
    const backups = fs.readdirSync(dir)
        .filter(file => file.startsWith(`${base}.backup-`))
        .map(file => ({
            name: file,
            path: path.join(dir, file),
            time: fs.statSync(path.join(dir, file)).mtime.getTime()
        }))
        .sort((a, b) => b.time - a.time); // 按时间降序排序

    // 如果备份文件数量超过限制，删除最旧的文件
    if (backups.length > maxBackups) {
        backups.slice(maxBackups).forEach(backup => {
            try {
                fs.unlinkSync(backup.path);
                console.log(`已删除旧备份文件: ${backup.name}`);
            } catch (err) {
                console.error(`删除旧备份文件失败: ${backup.name}`, err);
            }
        });
    }
}

// 优化写入hosts文件函数，添加文件存在性检查
async function writeToHosts(hostsEntries) {
    // 获取系统hosts路径
    const hostsPath = os.platform() === 'win32' 
        ? path.join(process.env.SystemRoot || 'C:\\Windows', 'System32\\drivers\\etc\\hosts')
        : '/etc/hosts';

    // 处理Windows文件名非法字符（替换冒号为横线）
    const timestamp = new Date().toISOString().replace(/:/g, '-');
    const backupPath = `${hostsPath}.backup-${timestamp.split('T')[0]}`;

    // 根据系统选择DNS刷新命令
    const dnsRefreshCommand = os.platform() === 'win32'
        ? 'ipconfig /flushdns'
        : 'sudo systemctl restart systemd-resolved';

    if (!fs.existsSync(hostsPath)) {
        throw new Error(`hosts文件不存在于 ${hostsPath}`);
    }

    return new Promise((resolve, reject) => {
        try {
            // 创建当日备份（如果不存在）
            if (!fs.existsSync(backupPath)) {
                fs.copyFileSync(hostsPath, backupPath);
                console.log(`备份已创建: ${backupPath}`);
                cleanupOldBackups(hostsPath);
            }

            // 读取并更新内容
            const originalContent = fs.readFileSync(hostsPath, 'utf8');
            const newContent = [
                `\n# DNS测试脚本添加 - ${new Date().toISOString()}`,
                ...hostsEntries,
                ''
            ].join('\n');

            // 写入新内容
            fs.writeFileSync(hostsPath, newContent);
            console.log('hosts文件更新成功');

            // 执行DNS刷新命令
            exec(dnsRefreshCommand, (error) => {
                if (error) {
                    console.error('DNS刷新失败:', error.message);
                    try {
                        fs.writeFileSync(hostsPath, originalContent);
                        console.log('已回滚到备份内容');
                    } catch (rollbackError) {
                        console.error('回滚失败:', rollbackError.message);
                    }
                    reject(error);
                    return;
                }
                console.log(os.platform() === 'win32' 
                    ? 'DNS缓存已刷新' 
                    : 'DNS服务已重启');
                resolve();
            });
        } catch (err) {
            console.error('操作失败:', err.message);
            if (err.code === 'EPERM') {
                console.log('\n请以管理员权限运行：');
                console.log('Windows: 右键点击VS Code -> 以管理员身份运行');
                console.log('macOS/Linux: 使用sudo命令运行脚本');
            }
            reject(err);
        }
    });
}


// 主测试函数
async function main() {
    // 添加命令行参数解析
    const skipConfirm = process.argv.includes('--skip-confirm');

    const domains = [
        'account.live.com',
        'login.windows.net',
        'logincdn.msftauth.net',
        'logincdn.msauth.net',
        'acctcdn.msftauth.net',
        'login.live.com',
        'fpt.live.com',
        'fpt6.microsoft.com',
        'ipv6.login.live.com',
        'fpt2.microsoft.com',
        'hostme.blob.core.windows.net',
        'browser.pipe.aria.microsoft.com',
        'browser.events.data.microsoft.com',
        'consentreceiverfd-prod.azurefd.net',
        'microsoftedgewelcome.microsoft.com',
        'msrewardspme.azureedge.net',
        'az15297.vo.msecnd.net',
        'storage.live.com',
        'www.bing.com',
        'cn.bing.com',
        'rewards.bing.com',
        'microsoft.com',
        'azure.microsoft.com',
        'login.microsoftonline.com',
        'vscode.dev',
        'visualstudio.com',
        'code.visualstudio.com',
        'github.com',
        'api.github.com',
        "raw.githubusercontent.com",
        'github.githubassets.com',
        'objects.githubusercontent.com',
        'prod.rewardsplatform.microsoft.com',
        "seedlog.igiven.com",
        "seedmail.igiven.com",
        "mail.igiven.com",
        "headline.igiven.com",
    ];

    const dnsServers = [
        '*******',        // Google
        '*******',        // Cloudflare
        '*********',      // 阿里
        '***************', // 114
        '*******',       // 微软云
        '************',// DNSPod
        '*******',       // CNNIC
        '************', // 腾讯云
        '************',// 百度
        '*************',//华为
        "***********",//字节
    ];

    console.log('开始DNS查询和连接测试...\n');
    const results = [];
    const failedQueries = [];

    for (const domain of domains) {
        console.log(`\n测试域名: ${domain}`);
        const uniqueIPs = new Set();
        let successfulQueries = 0;

        await Promise.all(dnsServers.map(async dnsServer => {
            try {
                const ips = await queryDNSWithRetry(domain, dnsServer);
                ips.forEach(ip => uniqueIPs.add(ip));
                successfulQueries++;
                console.log(`[成功] DNS服务器 ${dnsServer} 返回 ${ips.length} 个IP`);
            } catch (err) {
                failedQueries.push({ domain, dnsServer, error: err.message });
                console.error(`[失败] DNS服务器 ${dnsServer}: ${err.message}`);
            }
        }));

        console.log(`${domain} 查询成功率: ${(successfulQueries/dnsServers.length*100).toFixed(1)}%`);

        // 测试所有找到的IP
        await Promise.all([...uniqueIPs].map(async ip => {
            try {
                const responseTime = await testConnection(ip);
                results.push({ domain, ip, responseTime });
                console.log(`[成功] IP: ${ip}, 响应时间: ${responseTime}ms`);
            } catch (err) {
                console.error(`[失败] IP ${ip}: ${err.message}`);
            }
        }));
    }

    // 输出结果
    console.log('\n测试结果 (按响应时间排序):');
    console.log('域名\t\t\tIP地址\t\t\t响应时间(ms)');
    console.log('-'.repeat(80));

    results
        .sort((a, b) => a.responseTime - b.responseTime)
        .forEach(({ domain, ip, responseTime }) => {
            console.log(`${domain.padEnd(20)}${ip.padEnd(20)}${responseTime}ms`);
        });

    // 生成hosts建议
    console.log('\n推荐的hosts配置:');
    console.log('-'.repeat(50));

    const bestResults = new Map();
    results.forEach(result => {
        if (!bestResults.has(result.domain) ||
            bestResults.get(result.domain).responseTime > result.responseTime) {
            bestResults.set(result.domain, result);
        }
    });

    const hostsEntries = [];
    bestResults.forEach(result => {
        const entry = `${result.ip}\t${result.domain}`;
        console.log(entry);
        hostsEntries.push(entry);
    });

    if (skipConfirm) {
        try {
            await writeToHosts(hostsEntries);
            console.log('操作完成！');
        } catch (err) {
            console.error('操作失败:', err);
            process.exit(1);
        }
    } else {
        const rl = readline.createInterface({
            input: process.stdin,
            output: process.stdout
        });

        const answer = await new Promise(resolve => {
            rl.question('是否要将以上条目写入到hosts文件？(y/n) ', resolve);
        });
        rl.close();

        if (answer.toLowerCase() === 'y') {
            try {
                await writeToHosts(hostsEntries);
                console.log('操作完成！');
            } catch (err) {
                console.error('操作失败:', err);
                process.exit(1);
            }
        } else {
            console.log('操作已取消');
        }
    }
}

main().catch(console.error);



// vim /etc/hosts
//sudo systemctl restart systemd-resolved