import { Page } from 'playwright';
import { Logger } from './logger.service.js';
import { CheerioAPI, load } from 'cheerio'
import { QuizData } from '../types/index.js';
import { getRandomInRange } from '../utils/random.js';

//负责移动端任务的处理
export class ActivityService {
    private static instance: ActivityService;
    private logger = Logger.getInstance();

    private constructor() { }

    public static getInstance(): ActivityService {
        if (!ActivityService.instance) {
            ActivityService.instance = new ActivityService();
        }
        return ActivityService.instance;
    }


    async doABC(page: Page) {
        this.logger.info('ABC', 'Trying to complete poll')

        try {
            const html = await page.content()
            let $ = load(html)

            // Don't loop more than 15 in case unable to solve, would lock otherwise
            const maxIterations = 15
            let i
            for (i = 0; i < maxIterations && !$('span.rw_icon').length; i++) {
                await page.waitForSelector('.wk_OptionClickClass', { state: 'visible', timeout: 10000 })

                const answers = $('.wk_OptionClickClass')
                const answer = answers[getRandomInRange(0, 2)]?.attribs['id']

                await page.waitForSelector(`#${answer}`, { state: 'visible', timeout: 10000 })

                await new Promise(resolve => setTimeout(resolve, 2000))
                await page.click(`#${answer}`) // Click answer

                await new Promise(resolve => setTimeout(resolve, 4000))
                await page.waitForSelector('div.wk_button', { state: 'visible', timeout: 10000 })
                await page.click('div.wk_button') // Click next question button

                //没见过这个任务..所以不知道会不会打开新窗口...以后再看
                //page = await this.bot.browser.utils.getLatestTab(page)
                const html = await page.content()
                $ = load(html)
                await new Promise(resolve => setTimeout(resolve, 1000))
            }

            await new Promise(resolve => setTimeout(resolve, 4000))
            await page.close()

            if (i === maxIterations) {
                this.logger.info('ABC', 'Failed to solve quiz, exceeded max iterations of 15')
            } else {
                this.logger.info('ABC', 'Completed the ABC successfully')
            }

        } catch (error) {
            await page.close()
            this.logger.info('ABC', 'An error occurred:' + error)
        }
    }

    async doPoll(page: Page) {
        this.logger.info('POLL', 'Trying to complete poll')

        try {
            const buttonId = `#btoption${Math.floor(getRandomInRange(0, 1))}`

            await page.waitForSelector(buttonId, { state: 'visible', timeout: 10000 }).catch(() => { }) // We're gonna click regardless or not
            await new Promise(resolve => setTimeout(resolve, 2000))

            await page.click(buttonId)

            await new Promise(resolve => setTimeout(resolve, 4000))
            await page.close()

            this.logger.info('POLL', 'Completed the poll successfully')
        } catch (error) {
            await page.close()
            this.logger.info('POLL', 'An error occurred:' + error)
        }
    }



    async doQuiz(page: Page) {
        this.logger.info('QUIZ', 'Trying to complete quiz')

        try {
            // Check if the quiz has been started or not
            const quizNotStarted = await page.waitForSelector('#rqStartQuiz', { state: 'visible', timeout: 2000 }).then(() => true).catch(() => false)
            if (quizNotStarted) {
                await page.click('#rqStartQuiz')
            } else {
                this.logger.info('QUIZ', 'Quiz has already been started, trying to finish it')
            }

            await new Promise(resolve => setTimeout(resolve, 2000))

            let quizData = await this.getQuizData(page)
            const questionsRemaining = quizData.maxQuestions - quizData.CorrectlyAnsweredQuestionCount // Amount of questions remaining

            // All questions
            for (let question = 0; question < questionsRemaining; question++) {

                if (quizData.numberOfOptions === 8) {
                    const answers: string[] = []

                    for (let i = 0; i < quizData.numberOfOptions; i++) {
                        const answerSelector = await page.waitForSelector(`#rqAnswerOption${i}`, { state: 'visible', timeout: 10000 })
                        const answerAttribute = await answerSelector?.evaluate(el => el.getAttribute('iscorrectoption'))

                        if (answerAttribute && answerAttribute.toLowerCase() === 'true') {
                            answers.push(`#rqAnswerOption${i}`)
                        }
                    }

                    // Click the answers
                    for (const answer of answers) {
                        await page.waitForSelector(answer, { state: 'visible', timeout: 2000 })

                        // Click the answer on page
                        await page.click(answer)

                        const refreshSuccess = await this.waitForQuizRefresh(page)
                        if (!refreshSuccess) {
                            await page.close()
                            this.logger.info('QUIZ', 'An error occurred, refresh was unsuccessful')
                            return
                        }
                    }

                    // Other type quiz, lightspeed
                } else if ([2, 3, 4].includes(quizData.numberOfOptions)) {
                    quizData = await this.getQuizData(page) // Refresh Quiz Data
                    const correctOption = quizData.correctAnswer

                    for (let i = 0; i < quizData.numberOfOptions; i++) {

                        const answerSelector = await page.waitForSelector(`#rqAnswerOption${i}`, { state: 'visible', timeout: 10000 })
                        const dataOption = await answerSelector?.evaluate(el => el.getAttribute('data-option'))

                        if (dataOption === correctOption) {
                            // Click the answer on page
                            await page.click(`#rqAnswerOption${i}`)

                            const refreshSuccess = await this.waitForQuizRefresh(page)
                            if (!refreshSuccess) {
                                await page.close()
                                this.logger.info('QUIZ', 'An error occurred, refresh was unsuccessful')
                                return
                            }
                        }
                    }
                    await new Promise(resolve => setTimeout(resolve, 2000))
                }
            }

            // Done with
            await new Promise(resolve => setTimeout(resolve, 2000))
            await page.close()
            this.logger.info('QUIZ', 'Completed the quiz successfully')
        } catch (error) {
            await page.close()
            this.logger.info('QUIZ', 'An error occurred:' + error)
        }
    }


    async doThisOrThat(page: Page) {
        this.logger.info('THIS-OR-THAT', 'Trying to complete ThisOrThat')


        try {
            // Check if the quiz has been started or not
            const quizNotStarted = await page.waitForSelector('#rqStartQuiz', { state: 'visible', timeout: 2000 }).then(() => true).catch(() => false)
            if (quizNotStarted) {
                await page.click('#rqStartQuiz')
            } else {
                this.logger.info('THIS-OR-THAT', 'ThisOrThat has already been started, trying to finish it')
            }

            await new Promise(resolve => setTimeout(resolve, 2000))

            // Solving
            const quizData = await this.getQuizData(page)
            const questionsRemaining = quizData.maxQuestions - (quizData.currentQuestionNumber - 1) // Amount of questions remaining

            for (let question = 0; question < questionsRemaining; question++) {
                // Since there's no solving logic yet, randomly guess to complete
                const buttonId = `#rqAnswerOption${Math.floor(getRandomInRange(0, 1))}`
                await page.click(buttonId)

                const refreshSuccess = await this.waitForQuizRefresh(page)
                if (!refreshSuccess) {
                    await page.close()
                    this.logger.info('QUIZ', 'An error occurred, refresh was unsuccessful')
                    return
                }
            }

            this.logger.info('THIS-OR-THAT', 'Completed the ThisOrThat successfully')
        } catch (error) {
            await page.close()
            this.logger.info('THIS-OR-THAT', 'An error occurred:' + error)
        }
    }

    async doUrlReward(page: Page) {
        this.logger.info('URL-REWARD', 'Trying to complete UrlReward')

        try {
            await new Promise(resolve => setTimeout(resolve, 2000))

            await page.close()

            this.logger.info('URL-REWARD', 'Completed the UrlReward successfully')
        } catch (error) {
            await page.close()
            this.logger.info('URL-REWARD', 'An error occurred:' + error)
        }
    }


    async waitForQuizRefresh(page: Page): Promise<boolean> {
        try {
            await page.waitForSelector('span.rqMCredits', { state: 'visible', timeout: 10000 })
            await new Promise(resolve => setTimeout(resolve, 2000))

            return true
        } catch (error) {
            this.logger.info('QUIZ-REFRESH', 'An error occurred:' + error)
            return false
        }
    }

    async getQuizData(page: Page): Promise<QuizData> {
        try {
            const html = await page.content()
            const $ = load(html)

            const scriptContent = $('script').filter((index, element) => {
                return $(element).text().includes('_w.rewardsQuizRenderInfo')
            }).text()

            if (scriptContent) {
                const regex = /_w\.rewardsQuizRenderInfo\s*=\s*({.*?});/s
                const match = regex.exec(scriptContent)

                if (match && match[1]) {
                    const quizData = JSON.parse(match[1])
                    return quizData
                } else {
                    throw this.logger.info('GET-QUIZ-DATA', 'Quiz data not found within script')
                }
            } else {
                throw this.logger.info('GET-QUIZ-DATA', 'Script containing quiz data not found')
            }

        } catch (error) {
            throw this.logger.info('GET-QUIZ-DATA', 'An error occurred:' + error)
        }

    }

}
