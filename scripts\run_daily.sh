#!/bin/bash

# Set up environment variables
export PATH=$PATH:/usr/local/bin:/usr/bin:/bin:/usr/local/sbin:/usr/sbin:/sbin

# 设置默认accounts文件
ACCOUNTS_FILE=""

# 处理命令行参数
while getopts "f:" opt; do
  case $opt in
    f)
      ACCOUNTS_FILE="$OPTARG"
      ;;
    \?)
      echo "Invalid option: -$OPTARG" >&2
      exit 1
      ;;
  esac
done

# 其他环境设置
test -f $HOME/.nvm/nvm.sh && source $HOME/.nvm/nvm.sh


# 睡眠控制逻辑：只有当NO_SLEEP明确设置为"true"时才跳过睡眠
if [[ "${NO_SLEEP:-false}" != "true" ]]; then  # 使用:-设置默认值防止未定义变量
    
    # 配置睡眠时间参数（单位：秒）
    MINWAIT=$((5*60))    # 最小等待时间5分钟
    MAXWAIT=$((10*60))   # 最大等待时间10分钟
    
    # 参数有效性校验
    if (( MINWAIT > MAXWAIT )); then  # 防止最小值超过最大值
        echo "错误配置：MINWAIT（${MINWAIT}）不能大于MAXWAIT（${MAXWAIT}）" >&2
        exit 1
    fi
    
    # 生成随机等待时间（使用bash内置RANDOM变量）
    SLEEPTIME=$(( MINWAIT + RANDOM % (MAXWAIT - MINWAIT + 1) ))  # +1确保包含上限值
    
    # 转换时间为易读格式
    SLEEP_MINUTES=$(( SLEEPTIME / 60 ))      # 计算整分钟数
    REMAINING_SECONDS=$(( SLEEPTIME % 60 ))  # 计算剩余秒数
    
    # 输出带格式化时间的调试信息
    echo "正在休眠 ${SLEEP_MINUTES}分${REMAINING_SECONDS}秒 (总计: ${SLEEPTIME}秒)..."
    
    # 执行休眠命令
    sleep "${SLEEPTIME}"
else
    # 当明确设置NO_SLEEP=true时的处理
    echo "已跳过休眠（NO_SLEEP=${NO_SLEEP}）"
fi



if which killall &> /dev/null; then
    killall headless_shell
    killall node
else
    # Docker环境下不要安装psmisc包
    echo "提示: 在容器环境中'killall'不可用,这是正常情况 - 容器使用PID命名空间隔离"
fi



# 睡眠结束后再切换目录
cd $HOME/workspace/microsoft-rewards-script || {
    echo "错误：目录不存在"
    exit 1
}

# 使用accounts文件参数执行npm start
if [ -n "$ACCOUNTS_FILE" ]; then
    npm run start -- -f $ACCOUNTS_FILE
else
    npm run start
fi

