export * from "./reward.js";
export * from "./config.js";
export * from "./user.js";
export interface AccountInfo {
  email: string;
  password: string;
  proofEmail: string;
  score?:number;
  weight?: number; // 可选属性，表示账号被选中的权重，默认为 0
  disabled?: boolean; // 可选属性，表示账号是否被禁用，默认为 false
  pcSearchPointProgress?:number, //pc端搜索进度
  mobileSearchPointProgress?:number, //移动端搜索进度
  pcSearchCount?:number, //pc端搜索次数 每日真实的pc搜素次数
  mobileSearchCount?:number, //移动端搜索次数 每日真实的移动端搜素次数
  executions?:number, //执行次数
  maxDailyExecutionLimit?:number, //每日最大执行次数 默认值是-1 大于0 才有效
  maxSearchPerRequest?: number;  // 默认值是-1 大于0 才有效
  maxDailySearchLimit?:number; // 默认值是-1 大于0 才有效
  maxReadPerRequest?:number; //  默认值是-1  大于0 才有效
  maxDailyReadLimit?:number; //默认值是-1  大于0 才有效
  ignoreDistributionCycleDays? : boolean;
  onlyLogin?: boolean; //是否只登录
  //运行时数据
  dailyTask?: boolean  //是不是只执行每日任务
}



export interface OAuth {
  access_token: string;
  refresh_token: string;
  scope: string;
  expires_in: number;
  ext_expires_in: number;
  foci: string;
  token_type: string;
}