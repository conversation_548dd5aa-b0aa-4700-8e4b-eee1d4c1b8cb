{"name": "microsoft-reward", "version": "1.0.0", "author": "", "license": "ISC", "description": "", "type": "module", "scripts": {"dev": "cross-env DEV=true node --loader ts-node/esm src/index.ts -f ../src/accounts-dev.json ", "pc": "cross-env DEV=true node --loader ts-node/esm src/index.ts -t pc ", "build": "tsc && copyfiles -u 1  src/*.jsonc dist/", "preview": "node dist/index.js -f ../src/accounts.json", "start": "cross-env HEADLESS=true node dist/index.js", "test": "vitest", "config-schema": "npx typescript-json-schema src/types/config.ts AppConfig -o src/schema.json", "kill-chrome-win": "powershell -Command \"Get-Process | Where-Object {$_.Name -like '*chrome*' -or $_.Name -like '*chromium*'} | Stop-Process -Force\""}, "dependencies": {"cheerio": "^1.0.0", "commander": "^13.1.0", "dotenv": "^16.4.7", "fingerprint-generator": "^2.1.62", "fingerprint-injector": "^2.1.62", "jsonc-parser": "^3.3.1", "moment-timezone": "^0.5.47", "playwright": "^1.51.0", "undici": "^7.4.0", "winston": "^3.17.0"}, "devDependencies": {"@types/node": "^22.13.10", "copyfiles": "^2.4.1", "cross-env": "^7.0.3", "ts-node": "^10.9.2", "typescript": "^5.8.2", "typescript-json-schema": "^0.65.1", "vitest": "^3.0.8"}}