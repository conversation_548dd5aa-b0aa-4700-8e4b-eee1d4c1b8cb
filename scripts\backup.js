import * as fs from 'fs';
import path from 'path';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import os from 'os';

//process.argv[2]  =  部署路径
//process.argv[3]  =  SERVER_ID
//process.argv[4]  =  CF_API_TOKEN


const homeDir = os.homedir() || process.env.HOME || process.env.USERPROFILE || "";
const currentDir = process.cwd();
const entryDir = path.dirname(path.dirname(fileURLToPath(import.meta.url)));

//dotenv.config(); // 加载项目根目录的 .env 文件

// 定义搜索路径顺序
const envPaths = [
    path.join(entryDir, '.env'),
    path.join(entryDir, '.env.local'),
    path.join(currentDir, '.env'),
    path.join(currentDir, '.env.local'),
    path.join(homeDir, 'deploy', '.env'),
    path.join(homeDir, 'deploy', '.env.local'),
    path.join(homeDir, '.env'),
    path.join(homeDir, '.env.local'),
];

let loadedAnyFile = false;
// 遍历所有路径并加载存在的 .env 文件
for (const envPath of envPaths) {
    if (fs.existsSync(envPath)) {
        try {
            // 使用 override: false 确保后续文件不会覆盖已有的环境变量
            dotenv.config({
                path: envPath
            });
            console.log(`成功加载环境文件: ${envPath}`);
            loadedAnyFile = true;
        } catch (error) {
            console.error(`加载环境文件 ${envPath} 时出错:`, error);
        }
    }
    else {
        console.log(`环境文件不存在: ${envPath}`);
    }
}

const CF_ACCOUNT_ID = '9ba4c11ce3a9235a34b3481795485dec';
const CF_NAMESPACE_ID = 'aaf7485fd1b1447da5c1bdec45c40376';


// 获取命令行参数中的部署路径，如果没有则使用默认路径
const deployPath = process.argv[2] || path.join(currentDir, 'deploy');
const SERVER_ID = process.argv[3] || process.env.SERVER_ID;
const CF_API_TOKEN = process.argv[4] || process.env.CF_API_TOKEN;
const CF_API_BASE = `https://api.cloudflare.com/client/v4/accounts/${CF_ACCOUNT_ID}/storage/kv/namespaces/${CF_NAMESPACE_ID}`;


async function backup(deployPath, prefix) {
    try {
        // 读取所有匹配的文件
        const files = fs.readdirSync(deployPath);
        const accountFiles = files.filter(f => (f.startsWith('microsoft-accounts') || f.startsWith('accounts')) && !f.includes("dev") && f.endsWith('.json'));

        // 计算自纪元以来的天数
        const oneDay = 1000 * 60 * 60 * 24;
        const today = new Date().getTime();
        const days = Math.floor(today / oneDay);
        // 7天循环槽位（0-6）
        const archiveNumber = days % 7;
        for (const file of accountFiles) {
            const filePath = path.join(deployPath, file);
            const content = fs.readFileSync(filePath, 'utf-8');

            // 上传到 Cloudflare KV，添加前缀
            const response = await fetch(`${CF_API_BASE}/values/${prefix}_${file}_${archiveNumber}`, {
                method: 'PUT',
                headers: {
                    'Authorization': `Bearer ${CF_API_TOKEN}`,
                    'Content-Type': 'application/json'
                },
                body: content
            });

            if (!response.ok) {
                throw new Error(`Failed to backup ${filePath}: ${response.statusText}`);
            }

            console.log(`Successfully backed up ${filePath}`);
        }
    } catch (error) {
        console.error('Backup failed:', error);
    }
}

// 执行备份，传入部署路径和前缀（可选）
backup(deployPath, SERVER_ID);
