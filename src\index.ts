import { Command } from 'commander';
import { BrowserService } from './services/browser.service.js';
import { SessionService } from './services/session.service.js';
import { Logger } from './services/logger.service.js';
import { PCService } from './services/pc.service.js';
import { MobileService, AuthorizationCodeError } from './services/mobile.service.js';
import { SearchService } from './services/search.service.js';
import { AccountService } from './services/account.service.js';
import { AccountLockedError, LoginService } from './services/login.service.js';
import { Config } from './services/config.service.js';
import { debug_pause } from './utils/debug.js';
import { getDayOfYear, seededRandom } from './utils/random.js';
import moment from 'moment-timezone';

// Simple string hash function (djb2 variation)
function simpleHash(str: string): number {
  let hash = 5381;
  for (let i = 0; i < str.length; i++) {
    hash = (hash * 33) ^ str.charCodeAt(i);
  }
  return hash >>> 0; // Ensure positive integer
}

enum TaskType {
  REWARD = 'reward',
  MobileBrowser = 'mobile',
  PCBrowser = 'pc',
}

const program = new Command();
program
  .option('-f, --file <path>', '账号配置文件路径', "accounts.json")
  .option('-t, --task <type>', '要执行的任务类型', TaskType.REWARD)
  .parse(process.argv);
const options = program.opts();


//登录账号并领取奖励
async function reward() {
  const configService = Config.getInstance();
  await configService.loadRemoteConfig();
  const config = configService.getConfig();
  const logger = Logger.getInstance();

  // 检查当前小时是否小于配置的开始小时
  const currentMoment = moment().tz('Asia/Shanghai');

  // 处理 startHour 配置
  let startHour: number;
  if (config.startHour !== undefined) {
    if (config.startHour.includes('-')) {
      // 如果包含 '-'，则是范围格式（例如 '5-20'）
      const [min, max] = config.startHour.split('-').map(Number);
      // 使用基于当天日期和 serverId 哈希值的种子生成随机数
      const dayOfYear = currentMoment.dayOfYear();
      const serverIdHash = config.serverId ? simpleHash(config.serverId) : 0; // 计算 serverId 的哈希值，如果未定义则为 0
      const seed = dayOfYear + serverIdHash; // 组合种子
      startHour = seededRandom(min, max, seed);
      logger.info('System', `从范围 ${config.startHour} 中基于日期(${dayOfYear})和 ServerID哈希(${serverIdHash})组合种子(${seed})选择开始时间: ${startHour}`);
    } else {
      // 如果不包含 '-'，则直接转换为整数
      startHour = parseInt(config.startHour, 10);
    }

    const currentHour = currentMoment.hours();
    // 检查当前小时是否小于开始小时
    if (currentHour < startHour) {
      logger.info('System', `当前小时 ${currentHour} 小于配置的开始小时 ${startHour}，跳过执行`);
      return;
    }
  }

  const browserService = BrowserService.getInstance();
  try {
    await browserService.initBrowsers();
  }
  catch (error) {
    logger.error('System', `初始化浏览器失败: ${error}`);
    await browserService.dispose();
    return;
  }


  const sessionService = SessionService.getInstance();
  const accountsService = AccountService.getInstance();
  const loginService = LoginService.getInstance();
  const pcService = PCService.getInstance();
  const mobileService = MobileService.getInstance();
  const taskAccounts = await accountsService.loadAccounts(options.file);

  const date = moment().tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss.SSS');
  logger.log(`开始执行任务中:${date}`);
  // 处理每个账号的登录和任务
  for (const account of taskAccounts) {
    //日志加上换行符
    logger.logNF("\n=====================================================================================================\n");

    let score = 0;
    let pcSearchCount = 0;
    let mobileSearchCount = 0;
    let pcSearchPointProgress = 0;
    let mobileSearchPointProgress = 0;
    const pcContext = await browserService.createBrowserContext(account.email, false);
    const pcHomePage = await pcContext.newPage();
    try {
      await browserService.checkIPInfo(pcContext);
      await loginService.login(pcHomePage, account);
      await pcService.do(pcContext, pcHomePage, account);
      pcSearchCount = pcService.pcSearchCount;
      if (pcService.dashboardData) {
        score = pcService.dashboardData.userStatus.availablePoints;
        pcSearchPointProgress = pcService.pcSearchPointProgress(pcService.dashboardData);
        mobileSearchPointProgress = pcService.mobileSearchPointProgress(pcService.dashboardData);
      }
    } catch (error) {
      logger.error(account.email, `PC错误:${error}`);
    }
    finally {
      await sessionService.saveSessionData(await pcContext.cookies(), account.email, false);
      if (pcHomePage && pcHomePage.isClosed() === false) {
        await pcHomePage.close();
      }
      await pcContext.close();
    }

    //注释掉等待,减少执行时间
    // const waitTime = getRandomInRange(config.reward.doWaitMin, config.reward.doWaitMax);
    // logger.info(account.email, `等待 ${waitTime} 秒后开始移动端任务...`);
    // await new Promise(resolve => setTimeout(resolve, waitTime * 1000));

    logger.logNF("\n==================================================\n");

    const mobileContext = await browserService.createBrowserContext(account.email, true);
    const mobileHomePage = await mobileContext.newPage();
    let authorizationCodeError = false; // 标记是否跳过保存cookie
    try {
      await browserService.checkIPInfo(mobileContext);
      await loginService.login(mobileHomePage, account);
      await mobileService.do(mobileContext, mobileHomePage, account, pcSearchCount);
      mobileSearchCount = mobileService.mobileSearchCount;
      //保存今天的分数
      if (mobileService.userData) {
        score = mobileService.userData.balance;
        pcSearchPointProgress = mobileService.pcSearchPointProgress(mobileService.userData);
        mobileSearchPointProgress = mobileService.mobileSearchPointProgress(mobileService.userData);
      }
    } catch (error) {
      logger.error(account.email, `移动端错误:${error}`);
      // 检查是否是授权码错误
      if (error instanceof AuthorizationCodeError) {
        authorizationCodeError = true;
      }
    }
    finally {
      if (authorizationCodeError) {
        await sessionService.deleteSessionData(account.email, true);
      } else {
        await sessionService.saveSessionData(await mobileContext.cookies(), account.email, true);
      }
      if (mobileHomePage && mobileHomePage.isClosed() === false) {
        await mobileHomePage.close();
      }
      await mobileContext.close();
    }
    //只有登录成功.代表没有锁定错误和禁用错误才保存
    if (score != 0) {
      logger.important(account.email, `用户信息:${score}|${pcSearchPointProgress}|${mobileSearchPointProgress}|${pcSearchCount}|${mobileSearchCount}`);
    }
  }
  logger.log(`任务完成:${date}`);
  await browserService.dispose();
}

//打开手机浏览器做一些事情
async function mobileBrowser() {
  const browserService = BrowserService.getInstance();
  await browserService.initBrowsers();
  //随机生成一个字符串
  const randomString = Math.random().toString(36).substring(2);
  const context = await browserService.createBrowserContext(randomString, true);
  const page = await context.newPage();
  await page.goto('https://rewards.bing.com');
  await debug_pause("");
  await page.close();
  await context.close();
  await browserService.dispose();
}

//打开PC浏览器做一些事情
async function pcBrowser() {
  const browserService = BrowserService.getInstance();
  await browserService.initBrowsers();
  //随机生成一个字符串
  const randomString = Math.random().toString(36).substring(2);
  const context = await browserService.createBrowserContext(randomString, false);
  const page = await context.newPage();

  await debug_pause("");
  await page.close();
  await context.close();
  await browserService.dispose();
}

async function main() {
  const taskInput = options.task.toLowerCase();
  // 验证输入的任务类型是否有效
  if (!Object.values(TaskType).includes(taskInput as TaskType)) {
    throw new Error(`未知的任务类型: ${taskInput}`);
  }

  const task = taskInput as TaskType;
  switch (task) {
    case TaskType.REWARD:
      await reward();
      break;
    case TaskType.MobileBrowser:
      await mobileBrowser();
      break;
    case TaskType.PCBrowser:
      await pcBrowser();
      break;
    default:
      throw new Error(`未知的任务类型: ${task}`);
  }

}


main().catch((error) => {
  const logger = Logger.getInstance();
  logger.error("main", `${error}`);
});
