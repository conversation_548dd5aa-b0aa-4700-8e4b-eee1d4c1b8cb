import { Browser<PERSON>ontext, Page } from 'playwright';
import { Config } from './config.service.js';
import { Logger } from './logger.service.js';
import { AccountInfo, DashboardData, MorePromotion, PromotionalItem, PunchCard } from '../types/index.js';
import { SearchService } from './search.service.js';
import { CheerioAPI, load } from 'cheerio'
import { ActivityService } from './activity.service.js';
import { getRandomInRange, shuffleArray } from '../utils/random.js';
import { debug_pause } from '../utils/debug.js';
import { BrowserService } from './browser.service.js';

//负责桌面端的任务
export class PCService {

    private static instance: PCService;
    private logger = Logger.getInstance();
    private activityService = ActivityService.getInstance();
    private config = Config.getInstance().getConfig();
    public pcSearchCount: number = 0; //本次搜索了的pc端次数
    public dashboardData: DashboardData | undefined = undefined;

    public static getInstance(): PCService {
        if (!PCService.instance) {
            PCService.instance = new PCService();
        }
        return PCService.instance;
    }

    private constructor() { }
    /*
        //浏览器中执行
        const scripts = Array.from(document.querySelectorAll('script'));
        const targetScript = scripts.find(script => script.innerText.includes('var dashboard'));
        const regex = /var dashboard = (\{.*?\});/s;
        const match = regex.exec(targetScript.innerText);
        let dashboardData = null;
        if (match && match[1]) {
            dashboardData =  JSON.parse(match[1]);
        }
    */
    async getDashboardData(homePage: Page): Promise<DashboardData> {
        await homePage.reload({ waitUntil: 'networkidle' });
        const scriptContent = await homePage.evaluate(() => {
            const scripts = Array.from(document.querySelectorAll('script'));
            const targetScript = scripts.find(script => script.innerText.includes('var dashboard'));
            return targetScript?.innerText || null;
        });

        if (!scriptContent) {
            throw new Error('Dashboard data not found within script');
        }

        const dashboardData = await homePage.evaluate(scriptContent => {
            const regex = /var dashboard = (\{.*?\});/s;
            const match = regex.exec(scriptContent);
            if (match && match[1]) {
                return JSON.parse(match[1]);
            }
            return null;
        }, scriptContent);

        if (!dashboardData) {
            throw new Error('Unable to parse dashboard script');
        }

        return dashboardData;
    }


    async do(pcContext: BrowserContext, homePage: Page, account: AccountInfo): Promise<void> {
        if (account.onlyLogin) {
            return;
        }
        this.dashboardData = await this.getDashboardData(homePage);
        //当前点数
        const availablePoints = this.dashboardData.userStatus.availablePoints;
        this.logger.info(account.email, `当前点数:${availablePoints},开始执行PC任务`);
        //这个点数是pc搜索+移动搜索+activityAndQuiz(更多活动的),但是activityAndQuiz会有锁的一些活动,所以不准确
        //const dailyPoint = this.dashboardData.userStatus.counters.dailyPoint;

        // 定义任务数组
        const tasks = [];
        if (account.dailyTask) {
            tasks.push({ name: 'Daily Set', execute: () => this.handleDailySet(pcContext, this.dashboardData!, account) })
            tasks.push({ name: 'Activity And Quiz', execute: () => this.handleActivityAndQuiz(pcContext, this.dashboardData!, account) })
            tasks.push({ name: 'Punch Card', execute: () => this.handlePunchCard(pcContext, this.dashboardData!, account) })
        }
        else {
            tasks.push({ name: 'PC Search', execute: () => this.handlePCSearch(pcContext, this.dashboardData!, account, availablePoints) })
            tasks.push({ name: 'Daily Set', execute: () => this.handleDailySet(pcContext, this.dashboardData!, account) })
            tasks.push({ name: 'Activity And Quiz', execute: () => this.handleActivityAndQuiz(pcContext, this.dashboardData!, account) })
            tasks.push({ name: 'Punch Card', execute: () => this.handlePunchCard(pcContext, this.dashboardData!, account) })
            //tasks.push({ name: 'Mobile Search', execute: () => this.handleMobileSearch(this.dashboardData!, account) })
        }

        // 随机打乱任务顺序
        const shuffledTasks = shuffleArray(tasks);

        // 依次执行任务
        for (const task of shuffledTasks) {
            try {
                this.logger.info(account.email, `执行任务: ${task.name}`);
                await task.execute();
            } catch (error) {
                this.logger.error(account.email, `执行任务 ${task.name} 时出错: ${error}`);
            }
            //注释掉等待,减少执行时间
            //await new Promise(resolve => setTimeout(resolve, getRandomInRange(3000, 5000)));
        }
        this.dashboardData = await this.getDashboardData(homePage);
    }

    //处理移动端搜索
    async handleMobileSearch(dashboardData: DashboardData, account: AccountInfo) {
        const mobileSearch = dashboardData.userStatus.counters.mobileSearch;
        if (!mobileSearch) {
            return;
        }
        let mobileSearchEarnablePoints = 0;
        for (let i = 0; i < mobileSearch.length; i++) {
            const item = mobileSearch[i];
            mobileSearchEarnablePoints += (item.pointProgressMax - item.pointProgress);
        }
        if (mobileSearchEarnablePoints > 0) {
            this.logger.info(account.email, `mobileSearch尚有${mobileSearchEarnablePoints}分可领取`);
            //不处理..这是移动端的任务
        }
        else {
            this.logger.info(account.email, `mobileSearch今天已完成`);
        }
    }

    public pcSearchPointProgress(dashboardData: DashboardData): number {
        let pointProgress = 0;
        const pcSearch = dashboardData.userStatus.counters.pcSearch;
        if (!pcSearch) {
            return pointProgress;
        }
        for (let i = 0; i < pcSearch.length; i++) {
            const item = pcSearch[i];
            pointProgress += item.pointProgress;
        }
        return pointProgress;
    }

    public mobileSearchPointProgress(dashboardData: DashboardData): number {
        let pointProgress = 0;
        const mobileSearch = dashboardData.userStatus.counters.mobileSearch;
        if (!mobileSearch) {
            return pointProgress;
        }
        for (let i = 0; i < mobileSearch.length; i++) {
            const item = mobileSearch[i];
            pointProgress += item.pointProgress;
        }
        return pointProgress;
    }

    //开始pc搜索
    async handlePCSearch(pcContext: BrowserContext, dashboardData: DashboardData, account: AccountInfo, availablePoints: number) {
        this.pcSearchCount = 0;
        const pcSearch = dashboardData.userStatus.counters.pcSearch;
        if (!pcSearch) {
            return;
        }
        let pcSearchEarnablePoints = 0;
        for (let i = 0; i < pcSearch.length; i++) {
            const item = pcSearch[i];
            pcSearchEarnablePoints += (item.pointProgressMax - item.pointProgress);
        }
        if (pcSearchEarnablePoints == 0) {
            this.logger.info(account.email, `pc搜索今天已完成`);
            return;
        }
        //开始pc搜索
        this.logger.info(account.email, `pc搜索尚有${pcSearchEarnablePoints}分可领取`);

        const page = await pcContext.newPage();
        try {
            const searchTotalCount = (this.pcSearchPointProgress(dashboardData) + this.mobileSearchPointProgress(dashboardData)) / 3;
            this.pcSearchCount = await SearchService.getInstance().doSearch(page, pcSearchEarnablePoints / 3, availablePoints, searchTotalCount, account);
        }
        catch (error) {
            this.logger.error(account.email, `pc端搜索错误: ${error}`)
        }
        finally {
            await page.close()
        }
    }

    async handleActivityAndQuiz(pcContext: BrowserContext, dashboardData: DashboardData, account: AccountInfo) {
        //从morePromotions中获取,因为counters.activityAndQuiz有些锁上了额
        const activityAndQuiz = [...dashboardData.morePromotions];
        if (!activityAndQuiz) {
            return;
        }
        let activityAndQuizEarnablePoints = 0;
        const morePromotions: MorePromotion[] = [];
        for (let i = 0; i < activityAndQuiz.length; i++) {
            const item = activityAndQuiz[i];
            if (item.exclusiveLockedFeatureStatus === 'locked') {
                continue;
            }
            if (item.complete) {
                continue;
            }
            activityAndQuizEarnablePoints += (item.pointProgressMax - item.pointProgress);
            morePromotions.push(item);
        }
        if (activityAndQuizEarnablePoints == 0) {
            this.logger.info(account.email, `activityAndQuiz今天已完成`);
            return;
        }
        //开始activityAndQuiz
        this.logger.info(account.email, `activityAndQuiz尚有${activityAndQuizEarnablePoints}分可领取`);
        const page = await pcContext.newPage();
        try {
            await this.solveActivities(account, page, 'https://rewards.bing.com', morePromotions, "activityAndQuiz")
        }
        finally {
            await page.close()
        }
    }

    //处理每日任务
    async handleDailySet(pcContext: BrowserContext, dashboardData: DashboardData, account: AccountInfo) {
        const date = new Date();
        const today = `${(date.getMonth() + 1).toString().padStart(2, '0')}/${date.getDate().toString().padStart(2, '0')}/${date.getFullYear()}`;  //01/22/2025
        const dailySet = dashboardData.dailySetPromotions[today];
        if (!dailySet) {
            return;
        }
        let dailySetEarnablePoints = 0;
        const dailySetPromotions: PromotionalItem[] = [];
        for (let i = 0; i < dailySet.length; i++) {
            const item = dailySet[i];

            if (item.complete) {
                continue;
            }
            dailySetPromotions.push(item);
            dailySetEarnablePoints += (item.pointProgressMax - item.pointProgress);
        }

        //滚动播放的大屏上的点击活动
        //多一个activityProgressMax判断(但是不知道这个判断是否有效)..因为有点时候即使pointProgressMax有值.但是点了也没用
        //不要了..滚动播放的点击有的没有奖励
        // if (dashboardData.promotionalItem && dashboardData.promotionalItem.complete == false && dashboardData.promotionalItem.activityProgressMax > dashboardData.promotionalItem.activityProgress) {
        //     const point = dashboardData.promotionalItem.pointProgressMax - dashboardData.promotionalItem.pointProgress;
        //     if (point > 0) {
        //         dailySetEarnablePoints += point;
        //         (dashboardData.promotionalItem as any).isPromotionalItem = true;
        //         dailySetPromotions.push(dashboardData.promotionalItem);
        //     }
        // }

        if (dailySetEarnablePoints == 0) {
            this.logger.info(account.email, `dailySet今天已完成`);
            return;
        }

        //开始activityAndQuiz
        this.logger.info(account.email, `dailySet尚有${dailySetEarnablePoints}分可领取`);
        const page = await pcContext.newPage();
        try {
            await this.solveActivities(account, page, 'https://rewards.bing.com', dailySetPromotions, "dailySet")
        }
        finally {
            await page.close()
        }
    }

    //处理打卡任务
    async handlePunchCard(pcContext: BrowserContext, dashboardData: DashboardData, account: AccountInfo) {
        const punchCardsUncompleted = dashboardData.punchCards?.filter(x => !x.parentPromotion?.complete) ?? [] // Only return uncompleted punch cards
        if (!punchCardsUncompleted || punchCardsUncompleted.length == 0) {
            return;
        }
        const page = await pcContext.newPage();
        try {
            for (const punchCard of punchCardsUncompleted) {
                const activitiesUncompleted = punchCard.childPromotions.filter(x => !x.complete) // Only return uncompleted activities
                await this.solveActivities(account, page, punchCard.parentPromotion.destinationUrl, activitiesUncompleted, "punchCard")
            }
        }
        finally {
            await page.close()
        }
    }

    async solveActivities(account: AccountInfo, page: Page, pageUrl: string, activities: PromotionalItem[] | MorePromotion[], type: "dailySet" | "activityAndQuiz" | "punchCard") {
        const activityDelay = 1500;
        let newPage: Page | null = null;
        for (const activity of activities) {
            try {
                await page.goto(pageUrl, { waitUntil: "networkidle", timeout: 120000 })
                //处理选择器,然后打开活动的的页面
                let selector = `[data-bi-id="${activity.name}"]`
                if (type == "punchCard") {
                    try {
                        const html = await page.content()
                        const $ = load(html)
                        const element = $('.offer-cta').toArray().find(x => x.attribs.href?.includes(activity.offerId))
                        if (element) {
                            selector = `a[href*="${element.attribs.href}"]`
                        }
                    } catch (error) {
                        this.logger.info(account.email, 'An error occurred:' + error)
                    }
                }
                // 如果是大屏上的活动,isPromotionalItem是特意添加的属性
                if ("isPromotionalItem" in activity) {
                    selector = `#promo-item`
                }
                try {
                    this.logger.info(account.email, `开始活动${activity.title},type ${activity.promotionType}`);
                    const selectorLocator = page.locator(selector).last();
                    await selectorLocator.waitFor({ state: 'visible' });
                    newPage = await BrowserService.safeOpenPage(page, async () => await selectorLocator.click(), 15000)
                } catch (error) {
                    this.logger.error(account.email, `${type}:活动${activity.name}:选择${selector}失败,${error}`)
                }
                if (newPage == null) {
                    continue;
                }

                try {
                    await newPage.waitForLoadState('load', { timeout: 10000 });
                } catch (error) {
                    this.logger.info(account.email, '活动新页面打开失败(错了貌似也没关系):' + error)
                }

                //开始完成活动
                switch (activity.promotionType) {
                    // Quiz (Poll, Quiz or ABC)
                    case 'quiz':
                        switch (activity.pointProgressMax) {
                            // Poll or ABC (Usually 10 points)
                            case 10:
                                // Normal poll
                                if (activity.destinationUrl.toLowerCase().includes('pollscenarioid')) {
                                    this.logger.info(account.email, `Found activity type: "Poll" title: "${activity.title}"`)
                                    await this.activityService.doPoll(newPage)
                                } else { // ABC
                                    this.logger.info(account.email, `Found activity type: "ABC" title: "${activity.title}"`)
                                    await this.activityService.doABC(newPage)
                                }
                                break

                            // This Or That Quiz (Usually 50 points)
                            case 50:
                                this.logger.info(account.email, `Found activity type: "ThisOrThat" title: "${activity.title}"`)
                                await this.activityService.doThisOrThat(newPage)
                                break

                            // Quizzes are usually 30-40 points
                            default:
                                this.logger.info(account.email, `Found activity type: "Quiz" title: "${activity.title}"`)
                                await this.activityService.doQuiz(newPage)
                                break
                        }
                        break

                    // UrlReward (Visit)
                    case 'urlreward':
                        this.logger.info(account.email, `Found activity type: "UrlReward" title: "${activity.title}"`)
                        await this.activityService.doUrlReward(newPage)
                        break

                    // Misc, Usually UrlReward Type
                    default:
                        this.logger.info(account.email, `Found activity type: "Misc" title: "${activity.title}"`)
                        await this.activityService.doUrlReward(newPage)
                        break
                }
                await new Promise(resolve => setTimeout(resolve, activityDelay));
            }
            catch (error) {
                this.logger.info(account.email, 'An error occurred:' + error)
            } finally {
                // 确保关闭新页面（如果成功打开）
                if (newPage && !newPage.isClosed()) {
                    await newPage.close();
                    newPage = null;
                }
                // 切换回原始页面
                await page.bringToFront();
            }
        }
    }

}
