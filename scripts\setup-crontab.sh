#!/bin/bash

# crond目录
CRON_DIR="/etc/cron.d"

# 创建 cron.d 目录（如果不存在）
mkdir -p "$CRON_DIR"

# 复制 crontab 模板文件
cp -f "$HOME/workspace/microsoft-rewards-script/scripts/crontab.template" "$CRON_DIR/microsoft-rewards-cron"
if [ $? -ne 0 ]; then
  echo "Failed to copy crontab template file."
  exit 1
fi

# 设置文件权限和所有者
chmod 644 "$CRON_DIR/microsoft-rewards-cron"
chown "$USER:$USER" "$CRON_DIR/microsoft-rewards-cron"

# 应用 crontab
crontab "$CRON_DIR/microsoft-rewards-cron"