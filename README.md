# microtsoft-reward

## 养号规则

1. 不要异地登录...可能会被杀

2. 号码最少养三个月

3. 被封禁的号会连坐同ip的号..所以一个号被封了.那个服务器的号都得停一下

4. 不要在同一个ip连续申请兑换..会别封号

5. 账号换服务器的时候,可能会被封号..所以换服务器的话,就尽量不直接刷.需要养一段时间,比如每天只登录一次

## 安装说明

```
apt update

apt install rsync cron -y

curl -o- https://raw.githubusercontent.com/nvm-sh/nvm/v0.40.1/install.sh | bash
安装失败的话:
将仓库下载到root目录,并且重命名为.nvm
vim ~/.bashrc

# 输入
export NVM_DIR="$HOME/.nvm"
[ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"  # This loads nvm
[ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"  # This loads nvm bash_completion

# 使其生效
source ~/.bashrc

nvm install 23.0.0

npm config set registry https://registry.npmmirror.com

mkdir workspace
mkdir deploy
mkdir logs
mkdir sessions
mkdir cache
# 添加环境变量
vim deploy/.env

SERVER_ID=""
GODGODGAME_COM_TOKEN=""
IGIVEN_COM_TOKEN=""
LOG_API_TOKEN=""
ACCOUNT_API_TOKEN=""
PROXY_SERVER=""
REGION=""
LOG_DIR=""  # 日志目录cnb定义了日志目录,保存日志

# 直接运行命令
NO_SLEEP=true HEADLESS=true SESSION_DIR=$HOME/sessions CACHE_DIR=$HOME/cache LOG_DIR=$HOME/logs /bin/bash $HOME/workspace/microsoft-rewards-script/scripts/run_daily.sh

# 后台执行
nohup env NO_SLEEP=true HEADLESS=true SESSION_DIR=$HOME/sessions CACHE_DIR=$HOME/cache LOG_DIR=$HOME/logs /bin/bash $HOME/workspace/microsoft-rewards-script/scripts/run_daily.sh > $HOME/logs/mrsa-hour-$(date +\%H).log 2>&1 &

# 查看日志
cat ~/deploy/logs/mrsa_00.log 

# 打开/root/.ssh/authorized_keys
将ci时用到的公钥添加到服务器中..让ci可以正常运行


# 进入应用目录
npx playwright install-deps
npx playwright install 
npx playwright install chromium  

npx playwright@1.51.0 install-deps
npx playwright@1.51.0 install chromium

# 设置dns
sudo vim /etc/systemd/resolved.conf
[Resolve]
DNS=******* ******* ***************
FallbackDNS=******* *******
sudo systemctl restart systemd-resolved

./workspace/microsoft-rewards-script/scripts/ping.sh 
```

## cloudstudio 部署

```shell
#tzdata(时区) 
apt update && apt install rsync cron tzdata -y

# 设置时区
sudo rm /etc/localtime  # 删除现有的localtime文件
sudo ln -s /usr/share/zoneinfo/Asia/Shanghai /etc/localtime # 创建指向上海时区的符号链接
echo 'Asia/Shanghai' | sudo tee /etc/timezone # 更新timezone文件
# sudo timedatectl set-timezone Asia/Shanghai && timedatectl status  # docker环境不支持
# sudo dpkg-reconfigure tzdata  # 手动选择 5 和 69

# 如果是cloudstudio
mkdir -p $HOME/workspace/microsoft-rewards-script
mkdir -p $HOME/deploy
mkdir -p $HOME/logs
mkdir -p $HOME/sessions
mkdir -p $HOME/cache
TEMP_DIR=$(mktemp -d)
git clone https://cnb.cool/godgodgame/microsoft-reward.git "$TEMP_DIR"
cd "$TEMP_DIR"

rsync -av ./ $HOME/workspace/microsoft-rewards-script/
cd $HOME/workspace/microsoft-rewards-script
rm -rf "$TEMP_DIR"

npm config set registry https://registry.npmmirror.com

npm install 
npx playwright install-deps
npx playwright install chromium
npm run build

chmod +x $HOME/workspace/microsoft-rewards-script/scripts/*.sh
./scripts/setup-crontab.sh
echo "" > ./scripts/ping.sh 
echo "" > ./scripts/restart.sh 

vim $HOME/deploy/.env

SERVER_ID=""
GODGODGAME_COM_TOKEN=""
IGIVEN_COM_TOKEN=""
LOG_API_TOKEN=""
ACCOUNT_API_TOKEN=""
PROXY_SERVER=""
LOG_DIR=""

ln -s /root/logs /workspace/logs
ln -s /root/workspace/microsoft-rewards-script /workspace/microsoft-rewards-script
ln -s /root/deploy /workspace/deploy


##以后更新
GIT_USERNAME=cnb GIT_PASSWORD=xxx ./scripts/pull-deploy.sh
```

# playwright

当你执行 `npx playwright install chromium` 时，它会安装与你项目中 `package.json` 文件里指定的 `playwright` **版本相兼容**的 Chromium 版本。

解释如下：

1. `npx` 的行为：

   - `npx` 首先会检查你当前项目 `node_modules/.bin` 目录下是否存在 `playwright` 命令。

   - 因为你的 `package.json` 中指定了 `playwright` 的版本，并且你很可能已经执行过 `npm install` 或 `yarn install`，所以 `playwright` 已经被安装到了 `node_modules` 中。

   - 因此，`npx` 会执行你项目中 `node_modules` 里的那个特定版本的 `playwright` CLI。

2. `playwright install chromium` 的行为：

   - 这个命令是由你项目中安装的那个特定版本的 Playwright 来执行的。

   - Playwright 的每个版本都会对应一组特定版本的浏览器（Chromium, Firefox, WebKit），这些浏览器版本是经过 Playwright 团队测试并确保兼容的。

   - 所以，`install chromium` 子命令会下载并安装与你当前 Playwright 库版本相匹配的那个 Chromium 版本，而不是全球最新的 Chromium 版本。

当你在一个没有 `package.json` 文件的任意目录中执行 `npx playwright install chromium` 时：

1. `npx` 的行为：

   - `npx` 会首先检查当前目录和全局路径下是否已经安装了 `playwright` 包。

   - 由于这是一个“任意目录”且没有 `package.json`，我们假设本地没有预先安装的 `playwright`。

   - 在这种情况下，`npx` 会从 npm 仓库**下载最新版本 (latest) 的** `playwright` 包到一个临时的位置。

   - 然后，`npx` 会使用这个临时下载的最新版 `playwright` 来执行 `install chromium` 命令。

2. `playwright install chromium` 的行为 (使用最新版 Playwright)：

   - 这个最新版的 `playwright` 会下载一个与它自身版本**兼容的 Chromium 版本**。这不一定是互联网上绝对最新的 Chromium，而是 Playwright 最新版所绑定和测试过的那个 Chromium 版本。

   - Playwright 这样做是为了确保其 API 和浏览器驱动之间的稳定性和兼容性。

**Chromium 会安装到哪里？**

Playwright 会将下载的浏览器（包括 Chromium）安装到**用户主目录下的一个特定缓存文件夹**中。这个位置是独立于你执行命令的当前目录的。

具体路径因操作系统而异：

- **Linux 和 macOS**: `~/.cache/ms-playwright`

  - `~` 代表你的用户主目录 (e.g., `/home/<USER>

- **Windows**: `%USERPROFILE%\AppData\Local\ms-playwright`

  - `%USERPROFILE%` 代表你的用户主目录 (e.g., `C:\Users\<USER>