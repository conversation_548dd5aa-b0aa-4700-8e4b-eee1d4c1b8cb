import fs from 'fs';
import path from 'path';
import { <PERSON><PERSON>er<PERSON>ontext, <PERSON><PERSON> } from 'playwright';
import { Config } from './config.service.js';
import { Logger } from './logger.service.js';
import { BrowserFingerprintWithHeaders, FingerprintGenerator } from 'fingerprint-generator';
import { OperatingSystem,Device } from 'header-generator';
//用户session的创建和管理
export class SessionService {
  private static instance: SessionService;
  private config = Config.getInstance().getConfig();

  private static readonly MOBILE_COOKIES_FILE = 'mobile_cookies.json';
  private static readonly DESKTOP_COOKIES_FILE = 'desktop_cookies.json';
  // 添加元数据文件常量
  private static readonly FINGERPRINT_METADATA_FILE = 'fingerprint_metadata.json';

  private constructor() { }

  public static getInstance(): SessionService {
    if (!SessionService.instance) {
      SessionService.instance = new SessionService();
    }
    return SessionService.instance;
  }



  async loadSessionData(id: string, isMobile: boolean): Promise<Cookie[]> {
    try {
      const cookieFile = path.join(
        this.config.browser.sessionDir,
        id,
        isMobile ? SessionService.MOBILE_COOKIES_FILE : SessionService.DESKTOP_COOKIES_FILE
      );

      let cookies: Cookie[] = [];
      if (fs.existsSync(cookieFile)) {
        const cookiesData = await fs.promises.readFile(cookieFile, 'utf-8');
        cookies = JSON.parse(cookiesData);
      }

      return cookies;
    } catch (error) {
      throw new Error(error as string);
    }
  }

  async saveSessionData(cookies: Cookie[], id: string, isMobile: boolean) {
    try {
      const sessionDir = path.join(this.config.browser.sessionDir, id);

      if (!fs.existsSync(sessionDir)) {
        await fs.promises.mkdir(sessionDir, { recursive: true });
      }

      await fs.promises.writeFile(
        path.join(sessionDir, isMobile ? SessionService.MOBILE_COOKIES_FILE : SessionService.DESKTOP_COOKIES_FILE),
        JSON.stringify(cookies)
      );

      return sessionDir;
    } catch (error) {
      throw new Error(error as string);
    }
  }

  // 根据操作系统获取指纹文件名
  private getOperatingSystemFingerprintFile(operatingSystem: string): string {
    return `fingerprint_${operatingSystem}.json`;
  }

  async loadFingerprint(id: string,  operatingSystem: OperatingSystem): Promise<BrowserFingerprintWithHeaders> {
    try {
      const fingerprintFile = path.join(
        this.config.browser.sessionDir,
        id,
        this.getOperatingSystemFingerprintFile(operatingSystem)
      );

      // 检查指纹是否过期
      const isExpired = await this.isFingerprintExpired(id, operatingSystem);

      if (fs.existsSync(fingerprintFile) && !isExpired) {
        const fingerprintData = await fs.promises.readFile(fingerprintFile, 'utf-8');
        return JSON.parse(fingerprintData);
      }

      // 如果文件不存在或已过期,生成新的指纹
      const deviceType = operatingSystem === 'android' || operatingSystem === 'ios' ? 'mobile' : 'desktop';
      const newFingerprint = this.generateFingerprint(deviceType, operatingSystem);
      await this.saveFingerprintData(id, operatingSystem, newFingerprint);
      return newFingerprint;
    } catch (error) {
      throw new Error(error as string);
    }
  }

  private async isFingerprintExpired(id: string, operatingSystem: string): Promise<boolean> {
    try {
      const metadataFile = path.join(
        this.config.browser.sessionDir,
        id,
        SessionService.FINGERPRINT_METADATA_FILE
      );

      if (!fs.existsSync(metadataFile)) {
        return true; // 如果元数据文件不存在，视为过期
      }

      const metadataContent = await fs.promises.readFile(metadataFile, 'utf-8');
      const metadata = JSON.parse(metadataContent);

      if (!metadata[operatingSystem]) {
        return true; // 如果没有该操作系统的记录，视为过期
      }

      const lastGeneratedTime = new Date(metadata[operatingSystem]);
      const currentTime = new Date();

      // 检查是否过期（使用配置中的fingerprintExpirationDays参数）
      const expirationDays = this.config.browser.fingerprintExpirationDays || 7; // 默认7天
      const diffTime = currentTime.getTime() - lastGeneratedTime.getTime();
      const diffDays = diffTime / (1000 * 3600 * 24);

      return diffDays > expirationDays;
    } catch (error) {
      console.error('Error checking fingerprint expiration:', error);
      return true; // 出错时视为过期，重新生成
    }
  }

  async saveFingerprintData(id: string, operatingSystem: string, fingerprint: BrowserFingerprintWithHeaders) {
    try {
      const sessionDir = path.join(this.config.browser.sessionDir, id);

      if (!fs.existsSync(sessionDir)) {
        await fs.promises.mkdir(sessionDir, { recursive: true });
      }

      // 保存指纹数据
      const fingerprintFile = path.join(
        sessionDir,
        this.getOperatingSystemFingerprintFile(operatingSystem)
      );

      await fs.promises.writeFile(
        fingerprintFile,
        JSON.stringify(fingerprint)
      );

      // 更新元数据
      await this.updateFingerprintMetadata(id, operatingSystem);

      return sessionDir;
    } catch (error) {
      throw new Error(error as string);
    }
  }

  private async updateFingerprintMetadata(id: string, operatingSystem: string) {
    try {
      const metadataFile = path.join(
        this.config.browser.sessionDir,
        id,
        SessionService.FINGERPRINT_METADATA_FILE
      );

      let metadata:any = {};

      // 如果元数据文件存在，读取现有数据
      if (fs.existsSync(metadataFile)) {
        const metadataContent = await fs.promises.readFile(metadataFile, 'utf-8');
        metadata = JSON.parse(metadataContent);
      }

      // 更新当前操作系统的时间戳
      metadata[operatingSystem] = new Date().toISOString();

      // 写入元数据文件
      await fs.promises.writeFile(
        metadataFile,
        JSON.stringify(metadata, null, 2)
      );
    } catch (error) {
      console.error
      // 继续执行，不终止程序
    }
  }

  // 保持原有的generateFingerprint方法不变
  private generateFingerprint(device: Device, operatingSystem: OperatingSystem): BrowserFingerprintWithHeaders {
    return new FingerprintGenerator().getFingerprint({
      devices: [device],
      operatingSystems: [operatingSystem],
      browsers: [{ name: 'edge' }]
    });
  }

  /**
   * 删除指定ID的会话及其所有相关文件
   * @param id 会话ID
   * @returns 成功返回true，失败抛出错误
   */
  async deleteSession(id: string): Promise<boolean> {
    try {
      const sessionDir = path.join(this.config.browser.sessionDir, id);
      const logger = Logger.getInstance();

      // 检查会话目录是否存在
      if (!fs.existsSync(sessionDir)) {
        logger.log(`Session directory for ID ${id} does not exist.`);
        return true; // 如果目录不存在，视为删除成功
      }

      // 递归删除会话目录及其所有内容
      await fs.promises.rm(sessionDir, { recursive: true, force: true });

      logger.log(`Successfully deleted session with ID: ${id}`);
      return true;
    } catch (error) {
      const logger = Logger.getInstance();
      logger.log(`Failed to delete session with ID ${id}: ${error}`);
      throw new Error(`Failed to delete session: ${error}`);
    }
  }

  /**
   * 删除指定ID的会话数据（仅删除cookie文件，不删除整个会话目录）
   * @param id 会话ID
   * @param isMobile 是否为移动设备会话
   * @returns 成功返回true，失败抛出错误
   */
  async deleteSessionData(id: string, isMobile: boolean): Promise<boolean> {
    try {
      const cookieFile = path.join(
        this.config.browser.sessionDir,
        id,
        isMobile ? SessionService.MOBILE_COOKIES_FILE : SessionService.DESKTOP_COOKIES_FILE
      );
      const logger = Logger.getInstance();

      // 检查cookie文件是否存在
      if (!fs.existsSync(cookieFile)) {
        logger.log(`Cookie file for session ID ${id} (${isMobile ? 'mobile' : 'desktop'}) does not exist.`);
        return true; // 如果文件不存在，视为删除成功
      }

      // 删除cookie文件
      await fs.promises.unlink(cookieFile);
      //await fs.promises.writeFile(cookieFile, "[]");

      logger.log(`Successfully deleted ${isMobile ? 'mobile' : 'desktop'} cookies for session ID: ${cookieFile}`);
      return true;
    } catch (error) {
      const logger = Logger.getInstance();
      logger.log(`Failed to delete ${isMobile ? 'mobile' : 'desktop'} cookies for session ID ${id}: ${error}`);
      throw new Error(`Failed to delete session data: ${error}`);
    }
  }
}