import { Page } from 'playwright';
import { Config } from './config.service.js';
import { Logger } from './logger.service.js';
import { getRandomInRange, shuffleArray } from '../utils/random.js';
import { platform } from 'os'
import { debug_pause } from '../utils/debug.js';
import { BrowserService } from './browser.service.js';
import { ProxyAgent, Agent, setGlobalDispatcher, fetch } from 'undici';
import { AccountInfo } from '../types/index.js';
/**
 * 处理必应搜索相关的服务类
 */
export class SearchService {
    private static instance: SearchService;
    private logger = Logger.getInstance();
    private searchCache: Map<string, any[]> = new Map();
    private config = Config.getInstance().getConfig();
    private directAgent = new Agent();
    private bingSearchItems: string[] = [];
    private constructor() {
        // this.fetchBingSuggestions();
        this.fetchBingTrendingTopics();
        //this.fetchBingCarousel(); // 添加这一行
    }

    public static getInstance(): SearchService {
        if (!SearchService.instance) {
            SearchService.instance = new SearchService();
        }
        return SearchService.instance;
    }


    /**
     * 获取API数据并重试
     */
    private async fetchKeyWordsFromApi(api: string, maxRetries = 3, timeout = 10000): Promise<any> {
        let retries = 0;
        while (retries < maxRetries) {
            try {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), timeout);

                const response = await fetch(`${this.config.search.apiUrl}/${api}`, {
                    signal: controller.signal,
                    dispatcher: this.directAgent // 创建新的默认 Agent 实例,不使用代理
                });
                clearTimeout(timeoutId);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result: any = await response.json();
                if (!result.data) {
                    throw new Error('Invalid data format');
                }

                return result.data;
            } catch (error) {
                retries++;
                this.logger.info("Search", `Failed to fetch API ${api}, attempt ${retries}/${maxRetries}: ${error}`,);

                if (retries === maxRetries) {
                    throw new Error(`Failed to fetch API ${api} after ${maxRetries} attempts`);
                }

                // 指数退避重试
                await new Promise(resolve =>
                    setTimeout(resolve, Math.min(1000 * Math.pow(2, retries), 8000))
                );
            }
        }
    }

    /**
     * 获取必应搜索建议
     */
    private async fetchBingSuggestions(maxRetries = 3, timeout = 10000) {
        let retries = 0;
        const url = 'https://www.bingapis.com/api/v7/suggestions';
        const params = new URLSearchParams({
            q: '',
            appid: '6D0A9B8C5100E9ECC7E11A104ADD76C10219804B',
            cc: 'CN',
            setlang: 'zh-hans',
            history: '100'
        });

        while (retries < maxRetries) {
            try {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), timeout);

                const response = await fetch(`${url}?${params.toString()}`, {
                    signal: controller.signal,
                    dispatcher: this.directAgent
                });
                clearTimeout(timeoutId);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result: any = await response.json();
                if (!result.suggestionGroups?.[0]?.searchSuggestions) {
                    throw new Error('Invalid suggestions format');
                }

                // 提取所有查询词
                const queries = result.suggestionGroups[0].searchSuggestions
                    .map((suggestion: any) => suggestion.query)
                    .filter((query: string) => query);

                this.bingSearchItems.push(...queries);
                //this.logger.info("Search", `成功获取${queries.length}个必应搜索建议`);
                return;

            } catch (error) {
                retries++;
                this.logger.info("Search", `获取必应搜索建议失败, 尝试 ${retries}/${maxRetries}: ${error}`);

                if (retries === maxRetries) {
                    this.logger.info("Search", `获取必应搜索建议失败, 已达到最大重试次数`);
                    return;
                }

                // 指数退避重试
                await new Promise(resolve =>
                    setTimeout(resolve, Math.min(1000 * Math.pow(2, retries), 8000))
                );
            }
        }
    }

    /**
     * 获取必应热搜话题
     */
    private async fetchBingTrendingTopics(maxRetries = 3, timeout = 10000) {
        let retries = 0;
        const url = 'https://cn.bing.com/api/v7/news/trendingtopics';
        const params = new URLSearchParams({
            appid: '91B36E34F9D1B900E54E85A77CF11FB3BE5279E6',
            cc: 'CN',
            enabletag: '1'
        });

        while (retries < maxRetries) {
            try {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), timeout);

                const response = await fetch(`${url}?${params.toString()}`, {
                    signal: controller.signal,
                    dispatcher: this.directAgent
                });
                clearTimeout(timeoutId);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result: any = await response.json();
                if (!result.value) {
                    throw new Error('Invalid trending topics format');
                }

                // 提取所有话题的查询文本
                const queries = result.value
                    .map((topic: any) => topic.query?.text)
                    .filter((query: string) => query);

                this.bingSearchItems.push(...queries);
                this.logger.info("Search", `成功获取${queries.length}个必应热搜话题`);
                return;

            } catch (error) {
                retries++;
                this.logger.info("Search", `获取必应热搜话题失败, 尝试 ${retries}/${maxRetries}: ${error}`);

                if (retries === maxRetries) {
                    this.logger.info("Search", `获取必应热搜话题失败, 已达到最大重试次数`);
                    return;
                }

                await new Promise(resolve =>
                    setTimeout(resolve, Math.min(1000 * Math.pow(2, retries), 8000))
                );
            }
        }
    }

    /**
     * 获取必应轮播图数据
     */
    private async fetchBingCarousel(maxRetries = 3, timeout = 10000) {
        let retries = 0;
        const url = 'https://cn.bing.com/hp/api/v1/carousel';
        const params = new URLSearchParams({
            format: 'json',
            ecount: '32',
            efirst: '0'
        });

        while (retries < maxRetries) {
            try {
                const controller = new AbortController();
                const timeoutId = setTimeout(() => controller.abort(), timeout);

                const response = await fetch(`${url}?${params.toString()}`, {
                    signal: controller.signal,
                    dispatcher: this.directAgent
                });
                clearTimeout(timeoutId);

                if (!response.ok) {
                    throw new Error(`HTTP error! status: ${response.status}`);
                }

                const result: any = await response.json();
                if (!result.data?.[0]?.items) {
                    throw new Error('Invalid carousel data format');
                }

                // 提取所有标题
                const titles = result.data[0].items
                    .map((item: any) => item.title)
                    .filter((title: string) => title);

                this.bingSearchItems.push(...titles);
                //this.logger.info("Search", `成功获取${titles.length}个必应轮播图标题:${titles}`);
                return;

            } catch (error) {
                retries++;
                this.logger.info("Search", `获取必应轮播图数据失败, 尝试 ${retries}/${maxRetries}: ${error}`);

                if (retries === maxRetries) {
                    this.logger.info("Search", `获取必应轮播图数据失败, 已达到最大重试次数`);
                    return;
                }

                await new Promise(resolve =>
                    setTimeout(resolve, Math.min(1000 * Math.pow(2, retries), 8000))
                );
            }
        }
    }


    /**
     * 获取随机搜索次数
     */
    private getRandomSearchCount(maxSearchCount: number, range: number = 1) {
        // 計算隨機數的上下限,注意这里是0,保证可以做到不搜索.
        const min = Math.max(0, maxSearchCount - range);
        const max = maxSearchCount + range;
        // 生成隨機整數，包括min和max
        // Math.random() 生成 [0,1) 的浮點數
        // Math.floor(Math.random() * (max - min + 1)) 生成 [0, max-min] 的整數
        // 最後加上 min，得到 [min, max] 的整數
        return Math.floor(Math.random() * (max - min + 1)) + min;
    }

    /**
     * 执行必应搜索
     */
    public async doSearch(page: Page, searchCount: number, score: number, searchTotalCount: number, account: AccountInfo) {

        // 根据分数确定搜索限制,默认不限制
        let limitCount = this.config.search.defaultMaxSearchPerRequest;
        const searchLimit = Config.getInstance().getLimitByScore(score);

        // 首先检查账号中是否有自定义的搜索限制
        if (account && account.maxSearchPerRequest != undefined && account.maxSearchPerRequest >= 0) {
            // 如果账号中有自定义的搜索限制且大于0，则使用账号中的限制
            limitCount = account.maxSearchPerRequest;
        } else if (searchLimit && searchLimit.maxSearchPerRequest != undefined && searchLimit.maxSearchPerRequest >= 0) {
            // 如果账号中没有自定义限制，则使用分数对应的限制
            limitCount = searchLimit.maxSearchPerRequest;
        }
        // 检查账号中是否有每日搜索限制
        if (account && account.maxDailySearchLimit != undefined && account.maxDailySearchLimit >= 0) {
            let remainSearchCount = account.maxDailySearchLimit - searchTotalCount;
            remainSearchCount = Math.max(0, remainSearchCount);
            limitCount = Math.min(remainSearchCount, limitCount);
        }
        else if (searchLimit && searchLimit.maxDailySearchLimit != undefined && searchLimit.maxDailySearchLimit >= 0) {
            let remainSearchCount = searchLimit.maxDailySearchLimit - searchTotalCount;
            remainSearchCount = Math.max(0, remainSearchCount);
            limitCount = Math.min(remainSearchCount, limitCount);
        }

        if (limitCount <= 0) {
            return 0;
        }
        searchCount = Math.min(searchCount, this.getRandomSearchCount(limitCount));
        if (searchCount <= 0) {
            return 0;
        }


        // 随机选择两个API
        const selectedApis = shuffleArray(this.config.search.items).slice(0, 3);

        // 获取并缓存数据
        let searchItems: string[] = [...this.bingSearchItems];
        for (const api of selectedApis) {
            try {
                if (!this.searchCache.has(api)) {
                    const data = await this.fetchKeyWordsFromApi(api);
                    this.searchCache.set(api, data);
                }
                const cacheItems = this.searchCache.get(api);
                if (!cacheItems) continue;
                const items = cacheItems.map((x) => x.title);
                searchItems.push(...items);
            } catch (error) {
                this.logger.info("Search", `Failed to fetch data from ${api}:${error}`);
                continue; // 跳过失败的API，继续处理其他API
            }
        }

        if (searchItems.length === 0 || searchItems.length < searchCount) {
            throw new Error('检查搜索的api是否正确');
        }
        // 随机选择指定数量的搜索项
        searchItems = shuffleArray(searchItems);
        const selectedItems = searchItems.slice(0, searchCount);

        const maxRetries = 6; // 最大重试次数
        const retryDelay = 1500;
        let retries = 0;
        while (retries < maxRetries) {
            try {
                //如果3次都打开失败，就随机搜索一个关键词
                if (retries < 3) {
                    await page.goto("https://bing.com/", { waitUntil: 'networkidle', timeout: 60000 });
                }
                else {
                    const query = this.sanitizeTitle(searchItems[Math.floor(Math.random() * searchItems.length)]);
                    await page.goto(`https://bing.com/search?q=${query}`, { waitUntil: 'networkidle', timeout: 60000 });
                }

                try {
                    await page.waitForSelector('#bnp_close_icon:visible', { state: 'visible', timeout: 2000 })
                    await page.click('#bnp_close_icon:visible')
                }
                catch (error) {
                    this.logger.info('SEARCH', `未找到提示下载bing,继续执行: ${error}`)
                }

                // const header = page.locator('#hdr');
                // if (await header.count() > 0) {
                //     await header.evaluate((element: HTMLElement) => {
                //         element.style.display = 'none';
                //     });
                // }
                const searchInputBar = page.locator('#sb_form_q');
                await searchInputBar.click({ timeout: 5000 });
                break;
            } catch (error) {
                retries++;
                if (retries === maxRetries) {
                    throw new Error(`打开搜索页面失败: ${error}`);
                }
                this.logger.info("Search", `第${retries}次打开搜索页面失败: ${error}`);
                await new Promise(resolve => setTimeout(resolve, retryDelay));
            }
        }

        try {
            // 执行搜索
            for (const item of selectedItems) {
                const query = this.sanitizeTitle(item);
                this.logger.info("Search", `Searching for: ${query}`);
                await this.bingSearch(page, query);
                const randomWait = getRandomInRange(this.config.search.nextSearchMin, this.config.search.nextSearchMax);
                await new Promise(resolve => setTimeout(resolve, randomWait * 1000));
            }
        }
        catch (error) {
            this.logger.info("Search", `搜索失败: ${error}`);
        }

        return searchCount;
    }

    /**
     * 清理标题中的标点符号,如果有空格选择第一个单词
     */
    private sanitizeTitle(title: string): string {
        return title.replace(/[^\w\s\u4e00-\u9fa5]/g, '').trim().split(' ')[0];
    }

    /**
     * 执行必应搜索
     */
    private async bingSearch(searchPage: Page, query: string) {
        const platformControlKey = platform() === 'darwin' ? 'Meta' : 'Control'
        try {
            // Go to top of the page
            await searchPage.evaluate(() => {
                window.scrollTo(0, 0)
            })
            await new Promise(resolve => setTimeout(resolve, 500))


            const searchInputBar = searchPage.locator('#sb_form_q');
            await searchInputBar.click({ timeout: 5000 });

            await new Promise(resolve => setTimeout(resolve, 500))

            //清除输入框内容
            await searchPage.keyboard.down(platformControlKey)
            await searchPage.keyboard.press('A')
            await searchPage.keyboard.press('Backspace')
            await searchPage.keyboard.up(platformControlKey)
            //输入搜索内容
            await searchPage.keyboard.type(query, { delay: 100 })
            await searchPage.keyboard.press('Enter')

            await new Promise(resolve => setTimeout(resolve, 3000))

            if (this.config.search.scrollRandomResults) {
                await new Promise(resolve => setTimeout(resolve, 2000))
                await this.randomScroll(searchPage)
            }

            if (this.config.search.clickRandomResults) {
                await new Promise(resolve => setTimeout(resolve, 2000))
                await this.clickRandomLink(searchPage)
            }
        } catch (error) {
            this.logger.info('SEARCH', `搜索关键词${query}失败,An error occurred: ${error}`)
        }
    }

    private async randomScroll(page: Page) {
        try {
            const viewportHeight = await page.evaluate(() => window.innerHeight)
            const totalHeight = await page.evaluate(() => document.body.scrollHeight)
            const randomScrollPosition = Math.floor(Math.random() * (totalHeight - viewportHeight))

            await page.evaluate((scrollPos) => {
                window.scrollTo(0, scrollPos)
            }, randomScrollPosition)

        } catch (error) {
            this.logger.info('SEARCH-RANDOM-SCROLL', 'An error occurred:' + error)
        }
    }

    private async clickRandomLink(page: Page) {
        let newPage: Page | null = null;
        try {
            const elements = await page.locator('#b_results .b_algo :is(.b_algoheader, h2) a').all(); //h2是pc端的搜索结果.b_algoheader是移动端的搜索结果
            if (elements.length === 0) {
                this.logger.info('SEARCH-RANDOM-CLICK', 'No search results found');
                return;
            }
            const randomIndex = Math.floor(Math.random() * elements.length);
            const element = elements[randomIndex];

            // 获取链接的target属性
            const targetAttr = await element.getAttribute('target');
            const isNewTab = targetAttr === '_blank';

            if (isNewTab) {
                try {
                    await element.waitFor({ state: 'visible', timeout: 3000 }); // 等待元素可见
                    newPage = await BrowserService.safeOpenPage(page, async () => {
                        //await element.click(); //会打开同一个页面两次..
                        await element.evaluate(el => {
                            el.dispatchEvent(new MouseEvent('click', {
                                bubbles: true,
                                cancelable: true,
                                view: window
                            }));
                        });
                    }, 10000);
                    await newPage.waitForLoadState('load', { timeout: 10000 });
                    await new Promise(resolve => setTimeout(resolve, getRandomInRange(this.config.search.readTimeMin, this.config.search.readTimeMax) * 1000)); //等待5-10秒的阅读时间
                    await newPage.close();
                } catch (error) {
                    this.logger.info('SEARCH-RANDOM-CLICK', '搜索内容页面打开失败超时10秒');
                }
            } else {
                //移动端点击 当前页跳转处理
                const originalUrl = page.url();
                try {
                    await element.waitFor({ state: 'visible', timeout: 3000 }); // 等待元素可见
                } catch (error) {
                    this.logger.info("SEARCH", `点击搜索超时: ${error}`);
                    return;
                }
                await element.click();
                try {
                    await page.waitForSelector('#sacs_win:visible', { timeout: 3000 });
                    this.logger.info("SEARCH", `使用BING提示，点击继续执行`);
                    await page.click('#sacs_win:visible');
                    await element.click();
                } catch (error) {
                    this.logger.info("SEARCH", `无使用BING提示，继续执行: ${error}`);
                }
                await new Promise(resolve => setTimeout(resolve, getRandomInRange(this.config.search.readTimeMin, this.config.search.readTimeMax) * 1000)); //等待5-10秒的阅读时间
                if (page.url() !== originalUrl) {
                    await page.goto(originalUrl);
                }
            }
        } catch (error) {
            this.logger.info('SEARCH-RANDOM-CLICK', `Error: ${error}`);
        } finally {
            if (newPage && !newPage.isClosed()) {
                await newPage.close();
            }
            await page.bringToFront();
        }
    }
}
