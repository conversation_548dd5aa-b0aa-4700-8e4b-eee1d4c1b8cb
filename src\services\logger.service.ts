import winston from 'winston';
import path from 'path';
import { Config } from './config.service.js';
import moment from 'moment-timezone';
import fs from 'fs';
import { AppConfig } from '../types/config.js';


export class Logger {
  private static instance: Logger;
  private loggerNoFormat: winston.Logger;
  private logger: winston.Logger;
  private errorLogger: winston.Logger;
  private importantLogger: winston.Logger;
  private config: AppConfig = Config.getInstance().getConfig();
  private constructor() {

    // 清除七天前的日志
    this.cleanOldLogs();

    const { combine, timestamp, printf } = winston.format;
    const logFormat = printf(({ level, message, timestamp }) => {
      return `${timestamp} ${level}: ${message}`;
    });
    const timeFormat = timestamp({
      format: () => moment().tz('Asia/Shanghai').format('YYYY-MM-DD HH:mm:ss.SSS') // 替换为你的时区
    })

    this.loggerNoFormat = winston.createLogger({
      format: printf(({ message }) => {
        return `${message}`;
      }),
      transports: [
        new winston.transports.Console(),
        new winston.transports.File({
          filename: path.join(this.config.log.localDir, 'app.log')
        })
      ]
    });

    this.logger = winston.createLogger({
      format: combine(timeFormat, logFormat),
      transports: [
        new winston.transports.Console(),
        new winston.transports.File({
          filename: path.join(this.config.log.localDir, 'app.log')
        })
      ]
    });

    this.errorLogger = winston.createLogger({
      format: combine(timeFormat, logFormat),
      transports: [
        new winston.transports.File({
          filename: path.join(this.config.log.localDir, `error-${moment().tz('Asia/Shanghai').format('YYYY-MM-DD')}.log`)
        })
      ]
    });

    this.importantLogger = winston.createLogger({
      format: combine(timeFormat, logFormat),
      transports: [
        new winston.transports.File({
          filename: path.join(this.config.log.localDir, `important-${moment().tz('Asia/Shanghai').format('YYYY-MM-DD')}.log`)
        })
      ]
    });
  }

  /**
   * 清除七天前的日志文件
   */
  private cleanOldLogs(): void {
    try {
      const logDir = this.config.log.localDir;
      if (!fs.existsSync(logDir)) {
        fs.mkdirSync(logDir, { recursive: true });
      }
      // 检查 app.log 文件大小
      const appLogPath = path.join(logDir, 'app.log');
      if (fs.existsSync(appLogPath)) {
        const stats = fs.statSync(appLogPath);
        if (stats.size > 10240 * 1024) { // 500KB
          fs.writeFileSync(appLogPath, ''); // 清空文件
          console.log('app.log 超过 10MB，已清空文件');
        }
      }

      const files = fs.readdirSync(logDir);
      const now = moment().tz('Asia/Shanghai');
      const sevenDaysAgo = now.clone().subtract(this.config.log.saveDays, 'days');

      for (const file of files) {
        // 检查是否是日期格式的错误或重要日志文件
        if (file.startsWith('error-') || file.startsWith('important-')) {
          const dateStr = file.replace(/^(error-|important-)/, '').replace('.log', '');
          if (dateStr.match(/^\d{4}-\d{2}-\d{2}$/)) {
            const fileDate = moment(dateStr, 'YYYY-MM-DD');
            if (fileDate.isBefore(sevenDaysAgo)) {
              const filePath = path.join(logDir, file);
              fs.unlinkSync(filePath);
              console.log(`删除了旧日志文件: ${file}`);
            }
          }
        }
      }
    } catch (error) {
      console.error('清除旧日志文件时出错:', error);
    }
  }

  public static getInstance(): Logger {
    if (!Logger.instance) {
      Logger.instance = new Logger();
    }
    return Logger.instance;
  }

  public info(account: string, message: string): void {
    this.logger.info(`${account}:${message}`);
  }

  public error(account: string, message: string): void {
    this.info(account, message);
    this.errorLogger.error(`${account}:${message}`);
  }

  public important(account: string, message: string): void {
    this.importantLogger.info(`${account}:${message}`);
  }

  public log(message: string): void {
    this.logger.info(`${message}`);
  }

  public logNF(message: string): void {
    this.loggerNoFormat.info(`${message}`);
  }
}